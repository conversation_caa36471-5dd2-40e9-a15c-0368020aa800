#!/usr/bin/env python3
"""
配置向导
帮助用户设置SSH隧道和MongoDB连接
"""

import os
import click
from pathlib import Path


def create_env_file():
    """创建.env配置文件"""
    click.echo("=== CDN查询工具配置向导 ===\n")
    
    # 检查是否已存在.env文件
    if os.path.exists('.env'):
        if not click.confirm('.env文件已存在，是否覆盖？'):
            click.echo("配置取消")
            return
    
    # 询问是否使用SSH隧道
    use_ssh = click.confirm('是否需要通过SSH隧道连接MongoDB？')
    
    config_lines = []
    
    if use_ssh:
        click.echo("\n--- SSH隧道配置 ---")
        config_lines.append("# SSH隧道配置")
        config_lines.append("USE_SSH_TUNNEL=true")
        
        ssh_host = click.prompt('SSH服务器地址')
        config_lines.append(f"SSH_HOST={ssh_host}")
        
        ssh_port = click.prompt('SSH端口', default=22, type=int)
        config_lines.append(f"SSH_PORT={ssh_port}")
        
        ssh_username = click.prompt('SSH用户名')
        config_lines.append(f"SSH_USERNAME={ssh_username}")
        
        # 选择认证方式
        auth_method = click.prompt(
            '选择SSH认证方式',
            type=click.Choice(['key', 'password']),
            default='key'
        )
        
        if auth_method == 'key':
            default_key_path = str(Path.home() / '.ssh' / 'id_rsa')
            ssh_key_file = click.prompt('SSH私钥文件路径', default=default_key_path)
            config_lines.append(f"SSH_KEY_FILE={ssh_key_file}")
            config_lines.append("SSH_PASSWORD=")
            
            # 检查密钥文件是否存在
            if not os.path.exists(ssh_key_file):
                click.echo(f"⚠️  警告: 密钥文件 {ssh_key_file} 不存在")
                click.echo("请确保密钥文件存在，或使用以下命令生成:")
                click.echo(f"ssh-keygen -t rsa -b 4096 -f {ssh_key_file}")
        else:
            ssh_password = click.prompt('SSH密码', hide_input=True)
            config_lines.append(f"SSH_PASSWORD={ssh_password}")
            config_lines.append("SSH_KEY_FILE=")
        
        click.echo("\n--- 远程MongoDB配置 ---")
        remote_host = click.prompt('远程MongoDB主机', default='localhost')
        config_lines.append(f"REMOTE_MONGODB_HOST={remote_host}")
        
        remote_port = click.prompt('远程MongoDB端口', default=27017, type=int)
        config_lines.append(f"REMOTE_MONGODB_PORT={remote_port}")
        
        local_port = click.prompt('本地隧道端口', default=27018, type=int)
        config_lines.append(f"LOCAL_TUNNEL_PORT={local_port}")
        
        # MongoDB URI 在SSH模式下不直接使用
        config_lines.append("MONGODB_URI=mongodb://localhost:27017/")
        
    else:
        click.echo("\n--- 直接MongoDB连接配置 ---")
        config_lines.append("# 直接连接配置")
        config_lines.append("USE_SSH_TUNNEL=false")
        
        mongodb_uri = click.prompt('MongoDB连接URI', default='mongodb://localhost:27017/')
        config_lines.append(f"MONGODB_URI={mongodb_uri}")
    
    click.echo("\n--- 数据库配置 ---")
    database_name = click.prompt('数据库名称')
    config_lines.append(f"DATABASE_NAME={database_name}")
    
    collection_name = click.prompt('集合名称', default='cdn_statistics_total_metric')
    config_lines.append(f"COLLECTION_NAME={collection_name}")
    
    timezone = click.prompt('默认时区', default='Asia/Shanghai')
    config_lines.append(f"DEFAULT_TIMEZONE={timezone}")
    
    # 写入.env文件
    config_content = '\n'.join(config_lines) + '\n'
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    click.echo(f"\n✅ 配置文件已保存到 .env")
    
    # 显示下一步操作
    click.echo("\n--- 下一步操作 ---")
    if use_ssh:
        click.echo("1. 测试SSH连接: python main.py test-ssh")
    click.echo("2. 测试数据库连接: python main.py test-connection")
    click.echo("3. 开始查询数据: python main.py query -s 2025-05-20")


def show_current_config():
    """显示当前配置"""
    if not os.path.exists('.env'):
        click.echo("❌ .env文件不存在，请先运行配置向导")
        return
    
    click.echo("=== 当前配置 ===")
    with open('.env', 'r', encoding='utf-8') as f:
        content = f.read()
        click.echo(content)


@click.group()
def cli():
    """配置向导工具"""
    pass


@cli.command()
def setup():
    """运行配置向导"""
    create_env_file()


@cli.command()
def show():
    """显示当前配置"""
    show_current_config()


@cli.command()
def validate():
    """验证配置文件"""
    if not os.path.exists('.env'):
        click.echo("❌ .env文件不存在")
        return
    
    try:
        from config import Config
        click.echo("✅ 配置文件格式正确")
        
        # 显示关键配置
        click.echo(f"使用SSH隧道: {Config.USE_SSH_TUNNEL}")
        click.echo(f"数据库名称: {Config.DATABASE_NAME}")
        click.echo(f"集合名称: {Config.COLLECTION_NAME}")
        
        if Config.USE_SSH_TUNNEL:
            click.echo(f"SSH服务器: {Config.SSH_HOST}:{Config.SSH_PORT}")
            click.echo(f"SSH用户名: {Config.SSH_USERNAME}")
            
    except Exception as e:
        click.echo(f"❌ 配置文件有误: {e}")


if __name__ == '__main__':
    cli()
