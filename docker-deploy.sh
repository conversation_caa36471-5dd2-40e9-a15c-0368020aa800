#!/bin/bash

# CDN统计查询工具 - Docker部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查Docker环境
check_docker() {
    print_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未运行，请启动Docker"
        exit 1
    fi
    
    print_success "Docker环境检查通过"
}

# 检查配置文件
check_config() {
    print_info "检查配置文件..."
    
    if [ ! -f ".env" ]; then
        print_error ".env 配置文件不存在"
        print_info "请先运行: python setup_wizard.py setup"
        exit 1
    fi
    
    if [ ! -f ".env.bot" ]; then
        print_error ".env.bot 配置文件不存在"
        print_info "请先运行: python setup_bot.py setup"
        exit 1
    fi
    
    print_success "配置文件检查通过"
}

# 创建必要目录
create_directories() {
    print_info "创建必要目录..."
    
    mkdir -p logs exports temp
    
    print_success "目录创建完成"
}

# 构建Docker镜像
build_image() {
    print_info "构建Docker镜像..."
    
    docker build -t cdn-stats-tool:latest .
    
    if [ $? -eq 0 ]; then
        print_success "Docker镜像构建成功"
    else
        print_error "Docker镜像构建失败"
        exit 1
    fi
}

# 停止现有容器
stop_existing() {
    print_info "停止现有容器..."
    
    if docker ps -q -f name=cdn-stats-bot | grep -q .; then
        docker stop cdn-stats-bot
        docker rm cdn-stats-bot
        print_success "已停止现有容器"
    else
        print_info "没有运行中的容器"
    fi
}

# 启动容器
start_container() {
    print_info "启动容器..."
    
    docker run -d \
        --name cdn-stats-bot \
        --restart unless-stopped \
        -v "$(pwd)/.env:/app/.env:ro" \
        -v "$(pwd)/.env.bot:/app/.env.bot:ro" \
        -v "$(pwd)/logs:/app/logs" \
        -v "$(pwd)/exports:/app/exports" \
        -v "$(pwd)/temp:/app/temp" \
        -e TZ=Asia/Shanghai \
        cdn-stats-tool:latest
    
    if [ $? -eq 0 ]; then
        print_success "容器启动成功"
    else
        print_error "容器启动失败"
        exit 1
    fi
}

# 使用docker-compose启动
start_compose() {
    print_info "使用docker-compose启动..."
    
    if ! command -v docker-compose &> /dev/null; then
        print_warning "docker-compose未安装，使用docker命令启动"
        start_container
        return
    fi
    
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        print_success "docker-compose启动成功"
    else
        print_error "docker-compose启动失败"
        exit 1
    fi
}

# 查看状态
show_status() {
    print_info "容器状态:"
    docker ps -f name=cdn-stats
    
    echo ""
    print_info "查看日志命令:"
    echo "  docker logs -f cdn-stats-bot"
    echo "  docker-compose logs -f"
    
    echo ""
    print_info "进入容器命令:"
    echo "  docker exec -it cdn-stats-bot bash"
    
    echo ""
    print_info "停止容器命令:"
    echo "  docker stop cdn-stats-bot"
    echo "  docker-compose down"
}

# 测试连接
test_connection() {
    print_info "测试数据库连接..."
    
    docker run --rm \
        -v "$(pwd)/.env:/app/.env:ro" \
        -e START_MODE=test \
        cdn-stats-tool:latest
}

# 显示帮助
show_help() {
    echo "CDN统计查询工具 - Docker部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build     构建Docker镜像"
    echo "  start     启动容器"
    echo "  stop      停止容器"
    echo "  restart   重启容器"
    echo "  status    查看状态"
    echo "  logs      查看日志"
    echo "  test      测试连接"
    echo "  deploy    完整部署（构建+启动）"
    echo "  help      显示此帮助"
    echo ""
    echo "示例:"
    echo "  $0 deploy    # 完整部署"
    echo "  $0 start     # 启动容器"
    echo "  $0 logs      # 查看日志"
}

# 主函数
main() {
    case "${1:-deploy}" in
        "build")
            check_docker
            build_image
            ;;
        "start")
            check_docker
            check_config
            create_directories
            start_compose
            show_status
            ;;
        "stop")
            print_info "停止容器..."
            docker-compose down 2>/dev/null || docker stop cdn-stats-bot 2>/dev/null || true
            print_success "容器已停止"
            ;;
        "restart")
            $0 stop
            sleep 2
            $0 start
            ;;
        "status")
            show_status
            ;;
        "logs")
            if command -v docker-compose &> /dev/null; then
                docker-compose logs -f
            else
                docker logs -f cdn-stats-bot
            fi
            ;;
        "test")
            check_docker
            check_config
            build_image
            test_connection
            ;;
        "deploy")
            check_docker
            check_config
            create_directories
            build_image
            stop_existing
            start_compose
            show_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
