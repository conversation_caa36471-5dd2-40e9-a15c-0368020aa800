import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """数据库配置类"""

    # MongoDB 连接配置
    MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
    DATABASE_NAME = os.getenv('DATABASE_NAME', 'your_database')
    COLLECTION_NAME = os.getenv('COLLECTION_NAME', 'cdn_statistics_total_metric')

    # SSH 隧道配置
    USE_SSH_TUNNEL = os.getenv('USE_SSH_TUNNEL', 'false').lower() == 'true'
    SSH_HOST = os.getenv('SSH_HOST', '')
    SSH_PORT = int(os.getenv('SSH_PORT', '22'))
    SSH_USERNAME = os.getenv('SSH_USERNAME', '')
    SSH_PASSWORD = os.getenv('SSH_PASSWORD', '')
    SSH_KEY_FILE = os.getenv('SSH_KEY_FILE', '')

    # 远程MongoDB配置（通过SSH隧道访问）
    REMOTE_MONGODB_HOST = os.getenv('REMOTE_MONGODB_HOST', 'localhost')
    REMOTE_MONGODB_PORT = int(os.getenv('REMOTE_MONGODB_PORT', '27017'))

    # 本地隧道端口
    LOCAL_TUNNEL_PORT = int(os.getenv('LOCAL_TUNNEL_PORT', '27018'))

    # MySQL 配置
    MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
    MYSQL_PORT = int(os.getenv('MYSQL_PORT', '3306'))
    MYSQL_USERNAME = os.getenv('MYSQL_USERNAME', '')
    MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '')
    MYSQL_DATABASE = os.getenv('MYSQL_DATABASE', '')
    MYSQL_USER_TABLE = os.getenv('MYSQL_USER_TABLE', 'vc_user')
    MYSQL_USER_ID_FIELD = os.getenv('MYSQL_USER_ID_FIELD', 'id')
    MYSQL_USER_EMAIL_FIELD = os.getenv('MYSQL_USER_EMAIL_FIELD', 'email')

    # 是否启用用户email查询
    ENABLE_USER_EMAIL_LOOKUP = os.getenv('ENABLE_USER_EMAIL_LOOKUP', 'false').lower() == 'true'

    # 时区配置
    DEFAULT_TIMEZONE = os.getenv('DEFAULT_TIMEZONE', 'Asia/Shanghai')

    @classmethod
    def get_mongodb_config(cls):
        """获取MongoDB配置"""
        return {
            'uri': cls.MONGODB_URI,
            'database': cls.DATABASE_NAME,
            'collection': cls.COLLECTION_NAME
        }

    @classmethod
    def get_mysql_config(cls):
        """获取MySQL配置"""
        return {
            'host': cls.MYSQL_HOST,
            'port': cls.MYSQL_PORT,
            'username': cls.MYSQL_USERNAME,
            'password': cls.MYSQL_PASSWORD,
            'database': cls.MYSQL_DATABASE,
            'user_table': cls.MYSQL_USER_TABLE,
            'user_id_field': cls.MYSQL_USER_ID_FIELD,
            'user_email_field': cls.MYSQL_USER_EMAIL_FIELD,
            'enable_lookup': cls.ENABLE_USER_EMAIL_LOOKUP
        }

    @classmethod
    def get_ssh_config(cls):
        """获取SSH配置"""
        return {
            'use_ssh_tunnel': cls.USE_SSH_TUNNEL,
            'ssh_host': cls.SSH_HOST,
            'ssh_port': cls.SSH_PORT,
            'ssh_username': cls.SSH_USERNAME,
            'ssh_password': cls.SSH_PASSWORD,
            'ssh_key_file': cls.SSH_KEY_FILE,
            'remote_mongodb_host': cls.REMOTE_MONGODB_HOST,
            'remote_mongodb_port': cls.REMOTE_MONGODB_PORT,
            'local_tunnel_port': cls.LOCAL_TUNNEL_PORT
        }
