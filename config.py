import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """数据库配置类"""
    
    # MongoDB 连接配置
    MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
    DATABASE_NAME = os.getenv('DATABASE_NAME', 'your_database')
    COLLECTION_NAME = os.getenv('COLLECTION_NAME', 'cdn_statistics_total_metric')
    
    # 时区配置
    DEFAULT_TIMEZONE = os.getenv('DEFAULT_TIMEZONE', 'Asia/Shanghai')
    
    @classmethod
    def get_mongodb_config(cls):
        """获取MongoDB配置"""
        return {
            'uri': cls.MONGODB_URI,
            'database': cls.DATABASE_NAME,
            'collection': cls.COLLECTION_NAME
        }
