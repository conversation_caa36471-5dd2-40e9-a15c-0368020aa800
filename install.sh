#!/bin/bash

# CDN统计查询工具安装脚本
# 支持多种安装方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]]; then
        echo "windows"
    else
        echo "unknown"
    fi
}

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        print_error "未找到Python，请先安装Python 3.8或更高版本"
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
        print_error "Python版本需要3.8或更高，当前版本: $PYTHON_VERSION"
        exit 1
    fi
    
    print_success "Python版本: $PYTHON_VERSION"
}

# 检查pip
check_pip() {
    print_info "检查pip..."
    
    if command -v pip3 &> /dev/null; then
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        PIP_CMD="pip"
    else
        print_error "未找到pip，请先安装pip"
        exit 1
    fi
    
    print_success "pip可用: $PIP_CMD"
}

# 创建虚拟环境
create_venv() {
    local install_dir=$1
    
    print_info "创建虚拟环境..."
    
    cd "$install_dir"
    $PYTHON_CMD -m venv venv
    
    # 激活虚拟环境
    if [[ "$(detect_os)" == "windows" ]]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    
    print_success "虚拟环境创建成功"
}

# 安装依赖
install_dependencies() {
    print_info "安装依赖包..."
    
    # 升级pip
    $PIP_CMD install --upgrade pip
    
    # 安装依赖
    $PIP_CMD install -r requirements.txt
    
    print_success "依赖安装完成"
}

# 安装到系统
install_system() {
    print_info "安装到系统..."
    
    # 使用pip安装
    $PIP_CMD install .
    
    print_success "系统安装完成"
    print_info "现在可以使用以下命令:"
    print_info "  cdn-query         # 主查询工具"
    print_info "  cdn-setup         # 配置向导"
    print_info "  cdn-ssh-setup     # SSH配置向导"
    print_info "  cdn-batch-export  # 批量导出"
}

# 便携式安装
install_portable() {
    local install_dir=$1
    
    print_info "便携式安装到: $install_dir"
    
    # 创建安装目录
    mkdir -p "$install_dir"
    
    # 复制文件
    cp *.py "$install_dir/"
    cp *.md "$install_dir/"
    cp *.sh "$install_dir/"
    cp .env.example "$install_dir/"
    cp .env.ssh_template "$install_dir/"
    cp requirements.txt "$install_dir/"
    
    # 设置脚本权限
    chmod +x "$install_dir"/*.sh
    
    # 创建虚拟环境（可选）
    if [ "$USE_VENV" = "yes" ]; then
        create_venv "$install_dir"
        install_dependencies
    fi
    
    # 创建启动脚本
    create_portable_launchers "$install_dir"
    
    print_success "便携式安装完成"
    print_info "安装目录: $install_dir"
}

# 创建便携式启动脚本
create_portable_launchers() {
    local install_dir=$1
    
    # Linux/macOS启动脚本
    cat > "$install_dir/cdn-tool" << 'EOF'
#!/bin/bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

if [ -d "venv" ]; then
    source venv/bin/activate
fi

python main.py "$@"
EOF
    chmod +x "$install_dir/cdn-tool"
    
    # Windows批处理文件
    cat > "$install_dir/cdn-tool.bat" << 'EOF'
@echo off
cd /d "%~dp0"

if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
)

python main.py %*
EOF
    
    # 批量导出启动脚本
    cat > "$install_dir/batch-export" << 'EOF'
#!/bin/bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

if [ -d "venv" ]; then
    source venv/bin/activate
fi

python batch_export.py "$@"
EOF
    chmod +x "$install_dir/batch-export"
    
    cat > "$install_dir/batch-export.bat" << 'EOF'
@echo off
cd /d "%~dp0"

if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
)

python batch_export.py %*
EOF
}

# Docker安装
install_docker() {
    print_info "Docker安装..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_error "未找到Docker，请先安装Docker"
        exit 1
    fi
    
    # 构建镜像
    print_info "构建Docker镜像..."
    docker build -t cdn-stats-tool .
    
    # 创建docker-compose文件（如果不存在）
    if [ ! -f "docker-compose.yml" ]; then
        print_warning "docker-compose.yml不存在，请手动创建"
    fi
    
    print_success "Docker镜像构建完成"
    print_info "使用方法:"
    print_info "  docker run -it --rm cdn-stats-tool"
    print_info "  docker-compose up"
}

# 显示帮助
show_help() {
    echo "CDN统计查询工具安装脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "安装方式:"
    echo "  --system          安装到系统 (需要管理员权限)"
    echo "  --portable DIR    便携式安装到指定目录"
    echo "  --docker          Docker安装"
    echo "  --dev             开发模式安装"
    echo ""
    echo "选项:"
    echo "  --venv            使用虚拟环境 (便携式安装)"
    echo "  --help            显示此帮助"
    echo ""
    echo "示例:"
    echo "  $0 --system                    # 系统安装"
    echo "  $0 --portable /opt/cdn-tool    # 便携式安装"
    echo "  $0 --portable ~/cdn-tool --venv # 便携式安装+虚拟环境"
    echo "  $0 --docker                    # Docker安装"
}

# 主函数
main() {
    print_info "CDN统计查询工具安装程序"
    echo "==============================="
    
    # 解析参数
    INSTALL_TYPE=""
    INSTALL_DIR=""
    USE_VENV="no"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --system)
                INSTALL_TYPE="system"
                shift
                ;;
            --portable)
                INSTALL_TYPE="portable"
                INSTALL_DIR="$2"
                shift 2
                ;;
            --docker)
                INSTALL_TYPE="docker"
                shift
                ;;
            --dev)
                INSTALL_TYPE="dev"
                shift
                ;;
            --venv)
                USE_VENV="yes"
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定安装类型，显示菜单
    if [ -z "$INSTALL_TYPE" ]; then
        echo "请选择安装方式:"
        echo "1) 系统安装 (推荐)"
        echo "2) 便携式安装"
        echo "3) Docker安装"
        echo "4) 开发模式"
        echo ""
        read -p "请输入选择 (1-4): " choice
        
        case $choice in
            1) INSTALL_TYPE="system" ;;
            2) 
                INSTALL_TYPE="portable"
                read -p "安装目录 (默认: ./cdn-stats-tool): " INSTALL_DIR
                INSTALL_DIR=${INSTALL_DIR:-./cdn-stats-tool}
                ;;
            3) INSTALL_TYPE="docker" ;;
            4) INSTALL_TYPE="dev" ;;
            *) 
                print_error "无效选择"
                exit 1
                ;;
        esac
    fi
    
    # 检查环境
    if [ "$INSTALL_TYPE" != "docker" ]; then
        check_python
        check_pip
    fi
    
    # 执行安装
    case $INSTALL_TYPE in
        system)
            install_dependencies
            install_system
            ;;
        portable)
            install_portable "$INSTALL_DIR"
            ;;
        docker)
            install_docker
            ;;
        dev)
            install_dependencies
            print_success "开发环境安装完成"
            print_info "可以直接运行: python main.py"
            ;;
    esac
    
    print_success "🎉 安装完成！"
}

# 运行主函数
main "$@"
