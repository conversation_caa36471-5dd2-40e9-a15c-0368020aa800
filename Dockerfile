# CDN统计查询工具 Docker镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    openssh-client \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY *.py ./
COPY *.md ./
COPY *.sh ./
COPY .env.example ./
COPY .env.ssh_template ./

# 设置shell脚本执行权限
RUN chmod +x *.sh

# 创建数据目录
RUN mkdir -p /app/exports /app/logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口（如果需要）
# EXPOSE 8000

# 创建非root用户
RUN useradd -m -u 1000 cdnuser && chown -R cdnuser:cdnuser /app
USER cdnuser

# 默认命令
CMD ["python", "main.py", "--help"]
