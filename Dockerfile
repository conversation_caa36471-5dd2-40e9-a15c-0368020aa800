# CDN统计查询工具 - Docker镜像
FROM python:3.11-slim

# 设置维护者信息
LABEL maintainer="CDN Stats Tool"
LABEL description="CDN统计查询工具 - Telegram机器人版本"

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    openssh-client \
    curl \
    vim \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY *.py ./
COPY *.md ./

# 复制配置文件模板
COPY .env.example ./

# 创建必要的目录
RUN mkdir -p /app/logs \
    /app/exports \
    /app/temp \
    /tmp/cdn_bot \
    /root/.ssh

# 设置SSH目录权限
RUN chmod 700 /root/.ssh

# 创建非root用户（可选，提高安全性）
RUN useradd -m -u 1000 cdnuser && \
    chown -R cdnuser:cdnuser /app /tmp/cdn_bot

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "🚀 CDN统计查询工具 Docker容器启动"\n\
echo "================================"\n\
\n\
# 检查配置文件\n\
if [ ! -f "/app/.env" ]; then\n\
    echo "❌ 错误: .env 配置文件不存在"\n\
    echo "请挂载配置文件到 /app/.env"\n\
    echo "示例: docker run -v $(pwd)/.env:/app/.env cdn-stats-tool"\n\
    exit 1\n\
fi\n\
\n\
if [ ! -f "/app/.env.bot" ]; then\n\
    echo "❌ 错误: .env.bot 配置文件不存在"\n\
    echo "请挂载机器人配置文件到 /app/.env.bot"\n\
    echo "示例: docker run -v $(pwd)/.env.bot:/app/.env.bot cdn-stats-tool"\n\
    exit 1\n\
fi\n\
\n\
echo "✅ 配置文件检查通过"\n\
echo ""\n\
\n\
# 根据启动模式运行不同的程序\n\
case "${START_MODE:-bot}" in\n\
    "bot")\n\
        echo "🤖 启动Telegram机器人模式"\n\
        exec python start_bot.py\n\
        ;;\n\
    "cli")\n\
        echo "💻 启动命令行模式"\n\
        exec "$@"\n\
        ;;\n\
    "test")\n\
        echo "🔧 运行连接测试"\n\
        python main.py test-connection\n\
        python main.py test-mysql\n\
        python main.py test-ssh\n\
        ;;\n\
    *)\n\
        echo "❌ 未知的启动模式: ${START_MODE}"\n\
        echo "支持的模式: bot, cli, test"\n\
        exit 1\n\
        ;;\n\
esac\n\
' > /app/docker-entrypoint.sh && chmod +x /app/docker-entrypoint.sh

# 暴露端口（如果需要）
# EXPOSE 8080

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('https://api.telegram.org/bot' + open('.env.bot').read().split('TELEGRAM_BOT_TOKEN=')[1].split('\n')[0] + '/getMe', timeout=5)" || exit 1

# 切换到非root用户（可选）
# USER cdnuser

# 设置默认启动命令
ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD []
