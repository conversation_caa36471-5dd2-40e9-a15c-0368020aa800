#!/usr/bin/env python3
"""
Telegram机器人配置向导
"""

import os
import sys
import click
import requests


def validate_bot_token(token: str) -> bool:
    """验证Bot Token"""
    try:
        url = f"https://api.telegram.org/bot{token}/getMe"
        response = requests.get(url, timeout=10)
        return response.status_code == 200
    except:
        return False


def get_bot_info(token: str) -> dict:
    """获取机器人信息"""
    try:
        url = f"https://api.telegram.org/bot{token}/getMe"
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            return response.json().get('result', {})
    except:
        pass
    return {}


@click.group()
def cli():
    """Telegram机器人配置向导"""
    pass


@cli.command()
def setup():
    """配置Telegram机器人"""
    click.echo("🤖 CDN统计查询工具 - Telegram机器人配置向导")
    click.echo("=" * 50)
    
    # 检查主配置
    if not os.path.exists('.env'):
        click.echo("❌ 主配置文件 .env 不存在")
        click.echo("请先运行: python setup_wizard.py setup")
        return
    
    click.echo("✅ 找到主配置文件")
    
    # Bot Token配置
    click.echo("\n--- Telegram Bot Token 配置 ---")
    click.echo("1. 发送 /newbot 给 @BotFather")
    click.echo("2. 按提示设置机器人名称和用户名")
    click.echo("3. 获取Bot Token")
    click.echo("")
    
    while True:
        bot_token = click.prompt('请输入Bot Token', hide_input=True)
        
        click.echo("🔍 验证Bot Token...")
        if validate_bot_token(bot_token):
            bot_info = get_bot_info(bot_token)
            bot_name = bot_info.get('first_name', 'Unknown')
            bot_username = bot_info.get('username', 'Unknown')
            click.echo(f"✅ Bot Token有效")
            click.echo(f"机器人名称: {bot_name}")
            click.echo(f"机器人用户名: @{bot_username}")
            break
        else:
            click.echo("❌ Bot Token无效，请重新输入")
    
    # 用户权限配置
    click.echo("\n--- 用户权限配置 ---")
    click.echo("需要获取用户ID来设置权限")
    click.echo("获取方法: 发送任意消息给 @userinfobot")
    click.echo("")
    
    allowed_users = []
    while True:
        user_id_str = click.prompt('请输入允许使用的用户ID (输入空行结束)')
        if not user_id_str.strip():
            break
        
        try:
            user_id = int(user_id_str.strip())
            if user_id not in allowed_users:
                allowed_users.append(user_id)
                click.echo(f"✅ 已添加用户ID: {user_id}")
            else:
                click.echo("⚠️  用户ID已存在")
        except ValueError:
            click.echo("❌ 用户ID必须是数字")
    
    if not allowed_users:
        click.echo("❌ 至少需要添加一个用户ID")
        return
    
    # 管理员配置
    click.echo("\n--- 管理员配置 (可选) ---")
    admin_users = []
    
    if click.confirm('是否设置管理员用户？'):
        for user_id in allowed_users:
            if click.confirm(f'用户 {user_id} 是否为管理员？'):
                admin_users.append(user_id)
    
    # 功能限制配置
    click.echo("\n--- 功能限制配置 ---")
    max_query_days = click.prompt('最大查询天数', default=30, type=int)
    max_batch_days = click.prompt('最大批量导出天数', default=30, type=int)
    max_records = click.prompt('单次查询最大记录数', default=1000, type=int)
    max_file_size = click.prompt('最大文件大小(MB)', default=50, type=int)
    rate_limit = click.prompt('每用户每分钟最大请求数', default=10, type=int)
    
    # 生成配置文件
    config_content = f"""# Telegram机器人配置
# 由配置向导自动生成于 {click.DateTime().now()}

# Bot Token
TELEGRAM_BOT_TOKEN={bot_token}

# 允许使用的用户ID (逗号分隔)
TELEGRAM_ALLOWED_USERS={','.join(map(str, allowed_users))}

# 管理员用户ID (逗号分隔)
TELEGRAM_ADMIN_USERS={','.join(map(str, admin_users))}

# 功能限制
BOT_MAX_QUERY_DAYS={max_query_days}
BOT_MAX_BATCH_DAYS={max_batch_days}
BOT_MAX_RECORDS_PER_QUERY={max_records}
BOT_MAX_FILE_SIZE_MB={max_file_size}
BOT_RATE_LIMIT_PER_USER={rate_limit}

# 临时文件目录
BOT_TEMP_DIR=/tmp/cdn_bot
"""
    
    # 写入配置文件
    with open('.env.bot', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    click.echo(f"\n✅ 机器人配置已保存到 .env.bot")
    
    # 显示配置摘要
    click.echo("\n--- 配置摘要 ---")
    click.echo(f"机器人名称: {bot_info.get('first_name', 'Unknown')}")
    click.echo(f"机器人用户名: @{bot_info.get('username', 'Unknown')}")
    click.echo(f"允许用户数: {len(allowed_users)}")
    click.echo(f"管理员数: {len(admin_users)}")
    click.echo(f"最大查询天数: {max_query_days}")
    click.echo(f"最大批量导出天数: {max_batch_days}")
    
    # 显示下一步操作
    click.echo("\n--- 下一步操作 ---")
    click.echo("1. 安装机器人依赖: pip install -r requirements_bot.txt")
    click.echo("2. 启动机器人: python start_bot.py")
    click.echo("3. 在Telegram中搜索您的机器人并发送 /start")


@cli.command()
def test():
    """测试机器人配置"""
    click.echo("🔧 测试机器人配置")
    click.echo("=" * 30)
    
    # 检查配置文件
    if not os.path.exists('.env.bot'):
        click.echo("❌ 机器人配置文件 .env.bot 不存在")
        click.echo("请先运行: python setup_bot.py setup")
        return
    
    # 加载配置
    try:
        from dotenv import load_dotenv
        load_dotenv('.env.bot')
    except ImportError:
        click.echo("⚠️  python-dotenv 未安装")
    
    # 检查环境变量
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    allowed_users = os.getenv('TELEGRAM_ALLOWED_USERS')
    
    if not bot_token:
        click.echo("❌ TELEGRAM_BOT_TOKEN 未设置")
        return
    
    if not allowed_users:
        click.echo("❌ TELEGRAM_ALLOWED_USERS 未设置")
        return
    
    # 验证Bot Token
    click.echo("🔍 验证Bot Token...")
    if validate_bot_token(bot_token):
        bot_info = get_bot_info(bot_token)
        click.echo("✅ Bot Token有效")
        click.echo(f"机器人: {bot_info.get('first_name')} (@{bot_info.get('username')})")
    else:
        click.echo("❌ Bot Token无效")
        return
    
    # 验证用户ID
    click.echo("🔍 验证用户ID...")
    try:
        user_ids = [int(uid.strip()) for uid in allowed_users.split(',')]
        click.echo(f"✅ 允许的用户ID: {user_ids}")
    except ValueError:
        click.echo("❌ 用户ID格式错误")
        return
    
    # 检查依赖
    click.echo("🔍 检查依赖...")
    try:
        import telegram
        click.echo("✅ python-telegram-bot 已安装")
    except ImportError:
        click.echo("❌ python-telegram-bot 未安装")
        click.echo("请运行: pip install python-telegram-bot")
        return
    
    click.echo("\n🎉 配置测试通过！")
    click.echo("可以启动机器人: python start_bot.py")


@cli.command()
def info():
    """显示机器人信息"""
    if not os.path.exists('.env.bot'):
        click.echo("❌ 机器人配置文件不存在")
        return
    
    try:
        from dotenv import load_dotenv
        load_dotenv('.env.bot')
    except ImportError:
        pass
    
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not bot_token:
        click.echo("❌ Bot Token未配置")
        return
    
    bot_info = get_bot_info(bot_token)
    if bot_info:
        click.echo("🤖 机器人信息")
        click.echo("=" * 20)
        click.echo(f"名称: {bot_info.get('first_name')}")
        click.echo(f"用户名: @{bot_info.get('username')}")
        click.echo(f"ID: {bot_info.get('id')}")
        click.echo(f"是否为机器人: {bot_info.get('is_bot')}")
        
        allowed_users = os.getenv('TELEGRAM_ALLOWED_USERS', '')
        if allowed_users:
            user_count = len(allowed_users.split(','))
            click.echo(f"允许用户数: {user_count}")
    else:
        click.echo("❌ 无法获取机器人信息")


@cli.command()
def install():
    """安装机器人依赖"""
    click.echo("📦 安装Telegram机器人依赖...")
    
    import subprocess
    
    try:
        # 安装依赖
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements_bot.txt'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            click.echo("✅ 依赖安装成功")
        else:
            click.echo("❌ 依赖安装失败")
            click.echo(result.stderr)
    except Exception as e:
        click.echo(f"❌ 安装异常: {e}")


if __name__ == "__main__":
    cli()
