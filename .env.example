# MongoDB 配置
MONGODB_URI=mongodb://localhost:27017/
DATABASE_NAME=your_database_name
COLLECTION_NAME=cdn_statistics_total_metric

# SSH 隧道配置
USE_SSH_TUNNEL=true
SSH_HOST=your-ssh-server.com
SSH_PORT=22
SSH_USERNAME=root
SSH_PASSWORD=1YxY6rQGV4dJ3xdgQFnm
# 或者使用SSH密钥文件（推荐）
#SSH_KEY_FILE=/path/to/your/private_key

# 远程MongoDB配置（通过SSH隧道访问）
REMOTE_MONGODB_HOST=localhost
REMOTE_MONGODB_PORT=27017

# 本地隧道端口（避免与本地MongoDB冲突）
LOCAL_TUNNEL_PORT=27018

# MySQL 配置（用于查询用户email）
ENABLE_USER_EMAIL_LOOKUP=true
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USERNAME=your_mysql_username
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=your_mysql_database
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai
