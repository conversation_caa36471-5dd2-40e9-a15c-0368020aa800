('/Users/<USER>/PycharmProjects/every_day_statistices/build/cdn_tool/cdn-ssh-setup',
 True,
 False,
 True,
 None,
 None,
 False,
 False,
 None,
 True,
 False,
 'arm64',
 None,
 None,
 '/Users/<USER>/PycharmProjects/every_day_statistices/build/cdn_tool/cdn-ssh-setup.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-03.pyz',
   '/Users/<USER>/PycharmProjects/every_day_statistices/build/cdn_tool/PYZ-03.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/PycharmProjects/every_day_statistices/build/cdn_tool/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/PycharmProjects/every_day_statistices/build/cdn_tool/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/build/cdn_tool/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/PycharmProjects/every_day_statistices/build/cdn_tool/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('configure_ssh_tunnel',
   '/Users/<USER>/PycharmProjects/every_day_statistices/configure_ssh_tunnel.py',
   'PYSOURCE')],
 [],
 False,
 False,
 1749635890,
 [('run',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/bootloader/Darwin-64bit/run',
   'EXECUTABLE')],
 '/Library/Frameworks/Python.framework/Versions/3.13/Python')
