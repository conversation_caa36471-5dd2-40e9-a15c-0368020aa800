
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _winapi - imported by encodings (delayed, conditional, optional), shutil (conditional), ntpath (optional), subprocess (conditional), sysconfig (delayed), mimetypes (optional), multiprocessing.connection (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.reduction (conditional), multiprocessing.shared_memory (conditional), multiprocessing.heap (conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level)
missing module named nt - imported by shutil (conditional), importlib._bootstrap_external (conditional), ntpath (optional), os (delayed, conditional, optional), _colorize (delayed, conditional, optional), ctypes (delayed, conditional), _pyrepl.windows_console (delayed, optional)
missing module named 'collections.abc' - imported by logging (top-level), inspect (top-level), typing (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), traceback (top-level), setuptools (top-level), setuptools._distutils.filelist (top-level), setuptools._distutils.util (top-level), setuptools._vendor.jaraco.functools (top-level), setuptools._vendor.more_itertools.more (top-level), setuptools._vendor.more_itertools.recipes (top-level), setuptools._distutils._modified (top-level), setuptools._distutils.compat (top-level), setuptools._distutils.spawn (top-level), setuptools._distutils.compilers.C.base (top-level), setuptools._distutils.fancy_getopt (top-level), setuptools._reqs (top-level), http.client (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools._distutils.command.bdist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), configparser (top-level), setuptools._distutils.extension (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), setuptools.command.egg_info (top-level), setuptools._distutils.command.build (top-level), setuptools._distutils.command.sdist (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), wheel.cli.convert (top-level), wheel.cli.tags (top-level), setuptools._vendor.typing_extensions (top-level), bson.code (top-level), bson.codec_options (top-level), bson.son (top-level), pymongo.client_session (top-level), pymongo.aggregation (top-level), pymongo.bulk (top-level), cryptography.utils (top-level), cryptography.x509.name (top-level), cryptography.x509.base (top-level), cryptography.hazmat.bindings.openssl.binding (top-level), cryptography.x509.extensions (top-level), cryptography.x509.ocsp (top-level), dns.immutable (top-level), asyncio.base_events (top-level), asyncio.coroutines (top-level), pymongo.mongo_client (conditional), paramiko.hostkeys (top-level), tabulate (top-level), setuptools._distutils.command.build_ext (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), setuptools._distutils.compilers.C.msvc (top-level)
missing module named winreg - imported by importlib._bootstrap_external (conditional), platform (delayed, optional), mimetypes (optional), urllib.request (delayed, conditional, optional), dns.win32util (conditional), pymongo.pool (conditional, optional), setuptools._distutils.compilers.C.msvc (top-level), setuptools.msvc (conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named typing_extensions.Buffer - imported by setuptools._vendor.typing_extensions (top-level), wheel.wheelfile (conditional)
missing module named typing_extensions.Literal - imported by setuptools._vendor.typing_extensions (top-level), setuptools.config._validate_pyproject.formats (conditional)
missing module named typing_extensions.Self - imported by setuptools._vendor.typing_extensions (top-level), setuptools.config.expand (conditional), setuptools.config.pyprojecttoml (conditional), setuptools.config._validate_pyproject.error_reporting (conditional)
missing module named typing_extensions.deprecated - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.sysconfig (conditional), setuptools._distutils.command.bdist (conditional)
missing module named typing_extensions.TypeAlias - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.compilers.C.base (conditional), setuptools._reqs (conditional), setuptools.warnings (conditional), setuptools._path (conditional), setuptools._distutils.dist (conditional), setuptools.config.setupcfg (conditional), setuptools.config._apply_pyprojecttoml (conditional), setuptools.dist (conditional), setuptools.command.bdist_egg (conditional), setuptools.compat.py311 (conditional)
missing module named typing_extensions.Unpack - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.util (conditional), setuptools._distutils.compilers.C.base (conditional), setuptools._distutils.cmd (conditional)
missing module named typing_extensions.TypeVarTuple - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.util (conditional), setuptools._distutils.compilers.C.base (conditional), setuptools._distutils.cmd (conditional)
missing module named msvcrt - imported by subprocess (optional), click._winconsole (top-level), getpass (optional), click._termui_impl (conditional), multiprocessing.spawn (delayed, conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), _pyrepl.windows_console (top-level)
missing module named _overlapped - imported by asyncio.windows_events (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _wmi - imported by platform (optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _typeshed - imported by setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), pymongo.cursor (conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by /Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py (delayed)
missing module named wcwidth - imported by tabulate (optional)
missing module named SocketServer - imported by sshtunnel (conditional)
missing module named Queue - imported by sshtunnel (conditional)
missing module named invoke - imported by paramiko.config (optional)
missing module named 'pyasn1.codec' - imported by paramiko.ssh_gss (delayed)
missing module named pyasn1 - imported by paramiko.ssh_gss (delayed)
missing module named sspi - imported by paramiko.ssh_gss (optional)
missing module named sspicon - imported by paramiko.ssh_gss (optional)
missing module named pywintypes - imported by paramiko.ssh_gss (optional)
missing module named gssapi - imported by paramiko.ssh_gss (optional)
missing module named _winreg - imported by pymongo.pool (conditional, optional)
missing module named certifi - imported by pymongo.pyopenssl_context (optional)
missing module named 'OpenSSL.SSL' - imported by pymongo.ocsp_support (conditional)
missing module named 'requests.exceptions' - imported by pymongo.ocsp_support (top-level)
missing module named requests - imported by pymongo.ocsp_support (top-level)
missing module named 'service_identity.pyopenssl' - imported by pymongo.pyopenssl_context (top-level)
missing module named service_identity - imported by pymongo.pyopenssl_context (top-level)
missing module named OpenSSL - imported by pymongo.pyopenssl_context (top-level)
missing module named zstandard - imported by pymongo.compression_support (optional)
missing module named snappy - imported by pymongo.compression_support (optional)
missing module named kerberos - imported by pymongo.auth (optional)
missing module named winkerberos - imported by pymongo.auth (optional)
missing module named 'pymongo_auth_aws.auth' - imported by pymongo.auth_aws (optional)
missing module named pymongo_auth_aws - imported by pymongo.auth_aws (optional)
missing module named 'pymongocrypt.state_machine' - imported by pymongo.encryption (optional)
missing module named 'pymongocrypt.mongocrypt' - imported by pymongo.encryption (conditional, optional)
missing module named 'pymongocrypt.explicit_encrypter' - imported by pymongo.encryption (optional)
missing module named 'pymongocrypt.errors' - imported by pymongo.encryption (optional)
missing module named pymongocrypt - imported by pymongo.encryption (optional), pymongo.encryption_options (optional)
missing module named wmi - imported by dns.win32util (conditional)
missing module named pythoncom - imported by dns.win32util (conditional)
missing module named httpx - imported by dns._trio_backend (conditional), dns.query (conditional), dns.asyncquery (conditional), dns._asyncio_backend (conditional)
missing module named 'httpcore._backends' - imported by dns._trio_backend (conditional), dns.query (conditional), dns._asyncio_backend (conditional)
missing module named 'aioquic.quic' - imported by dns.quic._asyncio (top-level), dns.quic._common (top-level), dns.quic._sync (top-level), dns.quic._trio (top-level)
missing module named trio - imported by dns._trio_backend (top-level), dns.quic (conditional), dns.quic._trio (top-level)
missing module named 'aioquic.h3' - imported by dns.quic._common (top-level)
missing module named aioquic - imported by dns.quic (conditional)
missing module named sniffio - imported by dns.asyncbackend (delayed, optional)
missing module named httpcore - imported by dns._trio_backend (conditional), dns._asyncio_backend (conditional)
missing module named anyio - imported by dns._asyncio_backend (conditional)
missing module named 'trio.socket' - imported by dns._trio_backend (top-level)
missing module named idna - imported by dns.name (conditional)
missing module named colorama - imported by click._compat (delayed, conditional)
