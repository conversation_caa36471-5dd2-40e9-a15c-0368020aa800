(['/Users/<USER>/PycharmProjects/every_day_statistices/batch_export.py'],
 ['/Users/<USER>/PycharmProjects/every_day_statistices'],
 ['pymongo',
  'pymysql',
  'paramiko',
  'sshtunnel',
  'click',
  'tabulate',
  'python-dotenv',
  'python-dateutil',
  'urllib3',
  'cryptography',
  'bcrypt',
  'nacl'],
 [('/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('.env.example',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.env.example',
   'DATA'),
  ('.env.ssh_template',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.env.ssh_template',
   'DATA'),
  ('BATCH_EXPORT_GUIDE.md',
   '/Users/<USER>/PycharmProjects/every_day_statistices/BATCH_EXPORT_GUIDE.md',
   'DATA'),
  ('README.md',
   '/Users/<USER>/PycharmProjects/every_day_statistices/README.md',
   'DATA'),
  ('SSH_TUNNEL_SETUP.md',
   '/Users/<USER>/PycharmProjects/every_day_statistices/SSH_TUNNEL_SETUP.md',
   'DATA'),
  ('batch_export_csv.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/batch_export_csv.sh',
   'DATA'),
  ('export_daily_stats.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/export_daily_stats.sh',
   'DATA'),
  ('install.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/install.sh',
   'DATA'),
  ('package_for_colleagues.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/package_for_colleagues.sh',
   'DATA'),
  ('quick_start.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/quick_start.sh',
   'DATA'),
  ('requirements.txt',
   '/Users/<USER>/PycharmProjects/every_day_statistices/requirements.txt',
   'DATA')],
 '3.13.2 (v3.13.2:4f8bb3947cf, Feb  4 2025, 11:51:10) [Clang 15.0.0 '
 '(clang-1500.3.9.4)]',
 [('pyi_rth_inspect',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('batch_export',
   '/Users/<USER>/PycharmProjects/every_day_statistices/batch_export.py',
   'PYSOURCE')],
 [('_distutils_hack',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/util.py',
   'PYMODULE'),
  ('threading',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_threading_local.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('csv',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/csv.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/quoprimime.py',
   'PYMODULE'),
  ('string',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/string.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/__init__.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/generator.py',
   'PYMODULE'),
  ('copy',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/copy.py',
   'PYMODULE'),
  ('random',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/random.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/argparse.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shutil.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gzip.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compression.py',
   'PYMODULE'),
  ('struct',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/struct.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lzma.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bz2.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fnmatch.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gettext.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compat_pickle.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bisect.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/base64.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getopt.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/header.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/errors.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/utils.py',
   'PYMODULE'),
  ('socket',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/calendar.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/parse.py',
   'PYMODULE'),
  ('ipaddress',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ipaddress.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/quopri.py',
   'PYMODULE'),
  ('typing',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/typing.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/textwrap.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/glob.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_local.py',
   'PYMODULE'),
  ('glob',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/glob.py',
   'PYMODULE'),
  ('pathlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_abc.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/inspect.py',
   'PYMODULE'),
  ('token',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/token.py',
   'PYMODULE'),
  ('dis',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dis.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/opcode.py',
   'PYMODULE'),
  ('_opcode_metadata',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_opcode_metadata.py',
   'PYMODULE'),
  ('ast',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ast.py',
   'PYMODULE'),
  ('email',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/parser.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/feedparser.py',
   'PYMODULE'),
  ('json',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/scanner.py',
   'PYMODULE'),
  ('__future__',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/__future__.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tempfile.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_abc.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/abc.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py',
   'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/subprocess.py',
   'PYMODULE'),
  ('signal',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/signal.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/msvc.py',
   'PYMODULE'),
  ('unittest.mock',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/mock.py',
   'PYMODULE'),
  ('unittest',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/_log.py',
   'PYMODULE'),
  ('difflib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/difflib.py',
   'PYMODULE'),
  ('unittest.result',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/util.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipimport.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/selector_events.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py',
   'PYMODULE'),
  ('queue',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/queue.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/spawn.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/runpy.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/wintypes.py',
   'PYMODULE'),
  ('ctypes.util',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/secrets.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/request.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/netrc.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/__init__.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/error.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/compat/numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sysconfig/__init__.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_aix_support',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_aix_support.py',
   'PYMODULE'),
  ('_osx_support',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_osx_support.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('platform',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/platform.py',
   'PYMODULE'),
  ('_ios_support',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_ios_support.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/plistlib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('site',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site.py',
   'PYMODULE'),
  ('_pyrepl.main',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/main.py',
   'PYMODULE'),
  ('_pyrepl',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/__init__.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/curses.py',
   'PYMODULE'),
  ('curses',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/curses/has_key.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/keymap.py',
   'PYMODULE'),
  ('_pyrepl.types',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/types.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/commands.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/pager.py',
   'PYMODULE'),
  ('tty',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tty.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/reader.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/utils.py',
   'PYMODULE'),
  ('_colorize',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_colorize.py',
   'PYMODULE'),
  ('_pyrepl.console',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/console.py',
   'PYMODULE'),
  ('code',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/code.py',
   'PYMODULE'),
  ('codeop',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/codeop.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/trace.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/windows_console.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/readline.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/completing_reader.py',
   'PYMODULE'),
  ('rlcompleter',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/webbrowser.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shlex.py',
   'PYMODULE'),
  ('http.server',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py',
   'PYMODULE'),
  ('html',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc_data/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/configparser.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('wheel',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('backports',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/wheelfile.py',
   'PYMODULE'),
  ('wheel.util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/util.py',
   'PYMODULE'),
  ('wheel.cli',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/cli/__init__.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/cli/tags.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/cli/convert.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('wheel.vendored',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('wheel.metadata',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/metadata.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/cli/pack.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('tomllib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_types.py',
   'PYMODULE'),
  ('tomllib._re',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._shutil',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('nacl',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/__init__.py',
   'PYMODULE'),
  ('nacl.encoding',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/encoding.py',
   'PYMODULE'),
  ('nacl.exceptions',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/exceptions.py',
   'PYMODULE'),
  ('bcrypt',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bcrypt/__init__.py',
   'PYMODULE'),
  ('cryptography',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/openssl/binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/openssl/_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/openssl/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/backends/openssl/backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/decrepit/ciphers/algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/decrepit/ciphers/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/decrepit/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/serialization/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/serialization/ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/serialization/base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/backends/openssl/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/backends/__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/__about__.py',
   'PYMODULE'),
  ('cryptography.utils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/utils.py',
   'PYMODULE'),
  ('tabulate',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/tabulate/__init__.py',
   'PYMODULE'),
  ('tabulate.version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/tabulate/version.py',
   'PYMODULE'),
  ('sshtunnel',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/sshtunnel.py',
   'PYMODULE'),
  ('paramiko',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/__init__.py',
   'PYMODULE'),
  ('paramiko.common',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/common.py',
   'PYMODULE'),
  ('paramiko.proxy',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/proxy.py',
   'PYMODULE'),
  ('paramiko.config',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/config.py',
   'PYMODULE'),
  ('paramiko.hostkeys',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/hostkeys.py',
   'PYMODULE'),
  ('paramiko.pkey',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/pkey.py',
   'PYMODULE'),
  ('paramiko.agent',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/agent.py',
   'PYMODULE'),
  ('paramiko.file',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/file.py',
   'PYMODULE'),
  ('paramiko.packet',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/packet.py',
   'PYMODULE'),
  ('paramiko.message',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/message.py',
   'PYMODULE'),
  ('paramiko.sftp_file',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/sftp_file.py',
   'PYMODULE'),
  ('paramiko.sftp_si',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/sftp_si.py',
   'PYMODULE'),
  ('paramiko.sftp_handle',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/sftp_handle.py',
   'PYMODULE'),
  ('paramiko.sftp_attr',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/sftp_attr.py',
   'PYMODULE'),
  ('paramiko.sftp_server',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/sftp_server.py',
   'PYMODULE'),
  ('paramiko.sftp_client',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/sftp_client.py',
   'PYMODULE'),
  ('paramiko.sftp',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/sftp.py',
   'PYMODULE'),
  ('paramiko.ed25519key',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/ed25519key.py',
   'PYMODULE'),
  ('nacl.signing',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/signing.py',
   'PYMODULE'),
  ('nacl.utils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/utils.py',
   'PYMODULE'),
  ('nacl.public',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/public.py',
   'PYMODULE'),
  ('nacl.bindings',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/__init__.py',
   'PYMODULE'),
  ('nacl.bindings.utils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/utils.py',
   'PYMODULE'),
  ('nacl.bindings.sodium_core',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/sodium_core.py',
   'PYMODULE'),
  ('nacl.bindings.randombytes',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/randombytes.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_sign',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_sign.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_shorthash',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_shorthash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretstream',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_secretstream.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretbox',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_secretbox.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_scalarmult',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_scalarmult.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_pwhash',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_pwhash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_kx',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_kx.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_hash',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_hash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_generichash',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_generichash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_core',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_core.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_box',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_box.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_aead',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/bindings/crypto_aead.py',
   'PYMODULE'),
  ('paramiko.ecdsakey',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/ecdsakey.py',
   'PYMODULE'),
  ('paramiko.dsskey',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/dsskey.py',
   'PYMODULE'),
  ('paramiko.ber',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/ber.py',
   'PYMODULE'),
  ('paramiko.rsakey',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/rsakey.py',
   'PYMODULE'),
  ('paramiko.server',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/server.py',
   'PYMODULE'),
  ('paramiko.ssh_exception',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/ssh_exception.py',
   'PYMODULE'),
  ('paramiko.channel',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/channel.py',
   'PYMODULE'),
  ('paramiko.buffered_pipe',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/buffered_pipe.py',
   'PYMODULE'),
  ('paramiko.ssh_gss',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/ssh_gss.py',
   'PYMODULE'),
  ('paramiko.auth_strategy',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/auth_strategy.py',
   'PYMODULE'),
  ('paramiko.auth_handler',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/auth_handler.py',
   'PYMODULE'),
  ('paramiko.client',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/client.py',
   'PYMODULE'),
  ('paramiko.win_openssh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/win_openssh.py',
   'PYMODULE'),
  ('paramiko.win_pageant',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/win_pageant.py',
   'PYMODULE'),
  ('paramiko._winapi',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/_winapi.py',
   'PYMODULE'),
  ('paramiko.transport',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/transport.py',
   'PYMODULE'),
  ('paramiko.primes',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/primes.py',
   'PYMODULE'),
  ('paramiko.kex_gss',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/kex_gss.py',
   'PYMODULE'),
  ('paramiko.kex_ecdh_nist',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/kex_ecdh_nist.py',
   'PYMODULE'),
  ('paramiko.kex_group16',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/kex_group16.py',
   'PYMODULE'),
  ('paramiko.kex_group14',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/kex_group14.py',
   'PYMODULE'),
  ('paramiko.kex_group1',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/kex_group1.py',
   'PYMODULE'),
  ('paramiko.kex_gex',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/kex_gex.py',
   'PYMODULE'),
  ('paramiko.kex_curve25519',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/kex_curve25519.py',
   'PYMODULE'),
  ('paramiko.compress',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/compress.py',
   'PYMODULE'),
  ('paramiko.pipe',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/pipe.py',
   'PYMODULE'),
  ('paramiko.util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/util.py',
   'PYMODULE'),
  ('paramiko._version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/paramiko/_version.py',
   'PYMODULE'),
  ('pymysql',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/optionfile.py',
   'PYMODULE'),
  ('pymysql.cursors',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/cursors.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/constants/SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/constants/ER.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/constants/CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/constants/COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/constants/CLIENT.py',
   'PYMODULE'),
  ('pymysql.charset',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/_auth.py',
   'PYMODULE'),
  ('pymysql.times',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/times.py',
   'PYMODULE'),
  ('pymysql.err',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/err.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/constants/FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymysql/constants/__init__.py',
   'PYMODULE'),
  ('pymongo',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/__init__.py',
   'PYMODULE'),
  ('pymongo.write_concern',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/write_concern.py',
   'PYMODULE'),
  ('pymongo.errors',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/errors.py',
   'PYMODULE'),
  ('pymongo.typings',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/typings.py',
   'PYMODULE'),
  ('pymongo.collation',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/collation.py',
   'PYMODULE'),
  ('bson.typings',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/typings.py',
   'PYMODULE'),
  ('bson',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/__init__.py',
   'PYMODULE'),
  ('bson.tz_util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/tz_util.py',
   'PYMODULE'),
  ('bson.timestamp',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/timestamp.py',
   'PYMODULE'),
  ('bson._helpers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/_helpers.py',
   'PYMODULE'),
  ('bson.son',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/son.py',
   'PYMODULE'),
  ('bson.regex',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/regex.py',
   'PYMODULE'),
  ('bson.objectid',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/objectid.py',
   'PYMODULE'),
  ('bson.min_key',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/min_key.py',
   'PYMODULE'),
  ('bson.max_key',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/max_key.py',
   'PYMODULE'),
  ('bson.int64',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/int64.py',
   'PYMODULE'),
  ('bson.decimal128',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/decimal128.py',
   'PYMODULE'),
  ('bson.dbref',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/dbref.py',
   'PYMODULE'),
  ('bson.datetime_ms',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/datetime_ms.py',
   'PYMODULE'),
  ('bson.codec_options',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/codec_options.py',
   'PYMODULE'),
  ('bson.code',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/code.py',
   'PYMODULE'),
  ('bson.binary',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/binary.py',
   'PYMODULE'),
  ('uuid',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/uuid.py',
   'PYMODULE'),
  ('bson.raw_bson',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/raw_bson.py',
   'PYMODULE'),
  ('bson.errors',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/errors.py',
   'PYMODULE'),
  ('pymongo.read_preferences',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/read_preferences.py',
   'PYMODULE'),
  ('pymongo.topology_description',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/topology_description.py',
   'PYMODULE'),
  ('pymongo.server_type',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/server_type.py',
   'PYMODULE'),
  ('pymongo.server_description',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/server_description.py',
   'PYMODULE'),
  ('pymongo.hello',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/hello.py',
   'PYMODULE'),
  ('pymongo.server_selectors',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/server_selectors.py',
   'PYMODULE'),
  ('pymongo.operations',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/operations.py',
   'PYMODULE'),
  ('pymongo.bulk',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/bulk.py',
   'PYMODULE'),
  ('pymongo.pool',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/pool.py',
   'PYMODULE'),
  ('pymongo.read_concern',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/read_concern.py',
   'PYMODULE'),
  ('pymongo.pyopenssl_context',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/pyopenssl_context.py',
   'PYMODULE'),
  ('pymongo.ocsp_support',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/ocsp_support.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/x509/extensions.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/x509/name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/x509/general_name.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/x509/certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/types.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/x509/oid.py',
   'PYMODULE'),
  ('cryptography.x509.ocsp',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/x509/ocsp.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/x509/base.py',
   'PYMODULE'),
  ('pymongo.ocsp_cache',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/ocsp_cache.py',
   'PYMODULE'),
  ('cryptography.x509',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/x509/__init__.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/x509/verification.py',
   'PYMODULE'),
  ('pymongo.driver_info',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/driver_info.py',
   'PYMODULE'),
  ('pymongo.compression_support',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/compression_support.py',
   'PYMODULE'),
  ('pymongo.ssl_support',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/ssl_support.py',
   'PYMODULE'),
  ('pymongo.ssl_context',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/ssl_context.py',
   'PYMODULE'),
  ('pymongo.socket_checker',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/socket_checker.py',
   'PYMODULE'),
  ('pymongo.server_api',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/server_api.py',
   'PYMODULE'),
  ('pymongo.network',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/network.py',
   'PYMODULE'),
  ('pymongo.monitoring',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/monitoring.py',
   'PYMODULE'),
  ('pymongo.lock',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/lock.py',
   'PYMODULE'),
  ('pymongo.auth',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/auth.py',
   'PYMODULE'),
  ('pymongo.saslprep',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/saslprep.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/stringprep.py',
   'PYMODULE'),
  ('pymongo.auth_oidc',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/auth_oidc.py',
   'PYMODULE'),
  ('pymongo.auth_aws',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/auth_aws.py',
   'PYMODULE'),
  ('pymongo.message',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/message.py',
   'PYMODULE'),
  ('pymongo.mongo_client',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/mongo_client.py',
   'PYMODULE'),
  ('pymongo.encryption',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/encryption.py',
   'PYMODULE'),
  ('pymongo.results',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/results.py',
   'PYMODULE'),
  ('pymongo.encryption_options',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/encryption_options.py',
   'PYMODULE'),
  ('pymongo.daemon',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/daemon.py',
   'PYMODULE'),
  ('pymongo.server',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/server.py',
   'PYMODULE'),
  ('pymongo.response',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/response.py',
   'PYMODULE'),
  ('pymongo.topology',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/topology.py',
   'PYMODULE'),
  ('pymongo.settings',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/settings.py',
   'PYMODULE'),
  ('pymongo.command_cursor',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/command_cursor.py',
   'PYMODULE'),
  ('pymongo.client_options',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/client_options.py',
   'PYMODULE'),
  ('pymongo.change_stream',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/change_stream.py',
   'PYMODULE'),
  ('pymongo.aggregation',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/aggregation.py',
   'PYMODULE'),
  ('pymongo.cursor',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/cursor.py',
   'PYMODULE'),
  ('pymongo.collection',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/collection.py',
   'PYMODULE'),
  ('pymongo._version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/_version.py',
   'PYMODULE'),
  ('pymongo.monitor',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/monitor.py',
   'PYMODULE'),
  ('pymongo.srv_resolver',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/srv_resolver.py',
   'PYMODULE'),
  ('dns.resolver',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/resolver.py',
   'PYMODULE'),
  ('dns.win32util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/win32util.py',
   'PYMODULE'),
  ('dns._features',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/_features.py',
   'PYMODULE'),
  ('dns.tsig',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/tsig.py',
   'PYMODULE'),
  ('dns.reversename',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/reversename.py',
   'PYMODULE'),
  ('dns.rdtypes.svcbbase',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/svcbbase.py',
   'PYMODULE'),
  ('dns.rdtypes',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/__init__.py',
   'PYMODULE'),
  ('dns.wire',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/wire.py',
   'PYMODULE'),
  ('dns.tokenizer',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/tokenizer.py',
   'PYMODULE'),
  ('dns.ttl',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/ttl.py',
   'PYMODULE'),
  ('dns.renderer',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/renderer.py',
   'PYMODULE'),
  ('dns.rdtypes.util',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/util.py',
   'PYMODULE'),
  ('dns.immutable',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/immutable.py',
   'PYMODULE'),
  ('dns._immutable_ctx',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/_immutable_ctx.py',
   'PYMODULE'),
  ('dns.enum',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/enum.py',
   'PYMODULE'),
  ('dns.rdatatype',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdatatype.py',
   'PYMODULE'),
  ('dns.rdataclass',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdataclass.py',
   'PYMODULE'),
  ('dns.rcode',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rcode.py',
   'PYMODULE'),
  ('dns.query',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/query.py',
   'PYMODULE'),
  ('dns.xfr',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/xfr.py',
   'PYMODULE'),
  ('dns.zone',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/zone.py',
   'PYMODULE'),
  ('dns.zonetypes',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/zonetypes.py',
   'PYMODULE'),
  ('dns.zonefile',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/zonefile.py',
   'PYMODULE'),
  ('dns.rrset',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rrset.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ZONEMD',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/ZONEMD.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SOA',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/SOA.py',
   'PYMODULE'),
  ('dns.node',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/node.py',
   'PYMODULE'),
  ('dns.grange',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/grange.py',
   'PYMODULE'),
  ('dns.rdataset',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdataset.py',
   'PYMODULE'),
  ('dns.set',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/set.py',
   'PYMODULE'),
  ('dns.transaction',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/transaction.py',
   'PYMODULE'),
  ('dns.serial',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/serial.py',
   'PYMODULE'),
  ('dns.quic',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/quic/__init__.py',
   'PYMODULE'),
  ('dns.quic._trio',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/quic/_trio.py',
   'PYMODULE'),
  ('dns.quic._sync',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/quic/_sync.py',
   'PYMODULE'),
  ('dns.quic._common',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/quic/_common.py',
   'PYMODULE'),
  ('dns.quic._asyncio',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/quic/_asyncio.py',
   'PYMODULE'),
  ('dns._asyncbackend',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/_asyncbackend.py',
   'PYMODULE'),
  ('dns.asyncbackend',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/asyncbackend.py',
   'PYMODULE'),
  ('dns._asyncio_backend',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/_asyncio_backend.py',
   'PYMODULE'),
  ('dns.asyncresolver',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/asyncresolver.py',
   'PYMODULE'),
  ('dns.asyncquery',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/asyncquery.py',
   'PYMODULE'),
  ('dns._trio_backend',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/_trio_backend.py',
   'PYMODULE'),
  ('dns.nameserver',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/nameserver.py',
   'PYMODULE'),
  ('dns.rdata',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdata.py',
   'PYMODULE'),
  ('dns.rdtypes.txtbase',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/txtbase.py',
   'PYMODULE'),
  ('dns.rdtypes.tlsabase',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/tlsabase.py',
   'PYMODULE'),
  ('dns.rdtypes.nsbase',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/nsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.mxbase',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/mxbase.py',
   'PYMODULE'),
  ('dns.rdtypes.euibase',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/euibase.py',
   'PYMODULE'),
  ('dns.rdtypes.dsbase',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/dsbase.py',
   'PYMODULE'),
  ('dns.dnssectypes',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/dnssectypes.py',
   'PYMODULE'),
  ('dns.rdtypes.dnskeybase',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/dnskeybase.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.WKS',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/WKS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SVCB',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/SVCB.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SRV',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/SRV.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.PX',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/PX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP_PTR',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/NSAP_PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/NSAP.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NAPTR',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/NAPTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.KX',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/KX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.IPSECKEY',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/IPSECKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.HTTPS',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/HTTPS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.DHCID',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/DHCID.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.APL',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/APL.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.AAAA',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/AAAA.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.A',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/IN/__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.CH.A',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/CH/A.py',
   'PYMODULE'),
  ('dns.rdtypes.CH',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/CH/__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.X25',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/X25.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.WALLET',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/WALLET.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.URI',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/URI.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TXT',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/TXT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TSIG',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/TSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TLSA',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/TLSA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TKEY',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/TKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SSHFP',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/SSHFP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SPF',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/SPF.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SMIMEA',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/SMIMEA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RT',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/RT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RRSIG',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/RRSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RP',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/RP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RESINFO',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/RESINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.PTR',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPT',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/OPT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPENPGPKEY',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/OPENPGPKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3PARAM',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/NSEC3PARAM.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/NSEC3.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/NSEC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NS',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/NS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NINFO',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/NINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NID',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/NID.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.MX',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/MX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LP',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/LP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LOC',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/LOC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L64',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/L64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L32',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/L32.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ISDN',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/ISDN.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HIP',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/HIP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HINFO',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/HINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.GPOS',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/GPOS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI64',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/EUI64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI48',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/EUI48.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DS',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/DS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNSKEY',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/DNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNAME',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/DNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DLV',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/DLV.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CSYNC',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/CSYNC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CNAME',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/CNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CERT',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/CERT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDS',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/CDS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDNSKEY',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/CDNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CAA',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/CAA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AVC',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/AVC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AMTRELAY',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/AMTRELAY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AFSDB',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/rdtypes/ANY/AFSDB.py',
   'PYMODULE'),
  ('dns.name',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/name.py',
   'PYMODULE'),
  ('dns.message',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/message.py',
   'PYMODULE'),
  ('dns.update',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/update.py',
   'PYMODULE'),
  ('dns.opcode',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/opcode.py',
   'PYMODULE'),
  ('dns.entropy',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/entropy.py',
   'PYMODULE'),
  ('dns.ipv6',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/ipv6.py',
   'PYMODULE'),
  ('dns.ipv4',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/ipv4.py',
   'PYMODULE'),
  ('dns.inet',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/inet.py',
   'PYMODULE'),
  ('dns.flags',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/flags.py',
   'PYMODULE'),
  ('dns.exception',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/exception.py',
   'PYMODULE'),
  ('dns.edns',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/edns.py',
   'PYMODULE'),
  ('dns._ddr',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/_ddr.py',
   'PYMODULE'),
  ('dns',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/__init__.py',
   'PYMODULE'),
  ('dns.version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dns/version.py',
   'PYMODULE'),
  ('pymongo.uri_parser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/uri_parser.py',
   'PYMODULE'),
  ('pymongo.periodic_executor',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/periodic_executor.py',
   'PYMODULE'),
  ('pymongo.database',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/database.py',
   'PYMODULE'),
  ('pymongo.client_session',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/client_session.py',
   'PYMODULE'),
  ('pymongo.max_staleness_selectors',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/max_staleness_selectors.py',
   'PYMODULE'),
  ('pymongo.helpers',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/helpers.py',
   'PYMODULE'),
  ('pymongo.common',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/common.py',
   'PYMODULE'),
  ('pymongo._csot',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/_csot.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tracemalloc.py',
   'PYMODULE'),
  ('click.testing',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/testing.py',
   'PYMODULE'),
  ('click.core',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/core.py',
   'PYMODULE'),
  ('click.decorators',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/decorators.py',
   'PYMODULE'),
  ('click.shell_completion',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/shell_completion.py',
   'PYMODULE'),
  ('click.parser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/parser.py',
   'PYMODULE'),
  ('click.globals',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/globals.py',
   'PYMODULE'),
  ('click.exceptions',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/exceptions.py',
   'PYMODULE'),
  ('click.types',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/types.py',
   'PYMODULE'),
  ('click._compat',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/_winconsole.py',
   'PYMODULE'),
  ('click.utils',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/utils.py',
   'PYMODULE'),
  ('click.termui',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/_termui_impl.py',
   'PYMODULE'),
  ('click.formatting',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/_textwrap.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_strptime.py',
   'PYMODULE'),
  ('click',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/click/__init__.py',
   'PYMODULE'),
  ('cdn_query_tool',
   '/Users/<USER>/PycharmProjects/every_day_statistices/cdn_query_tool.py',
   'PYMODULE'),
  ('mysql_manager',
   '/Users/<USER>/PycharmProjects/every_day_statistices/mysql_manager.py',
   'PYMODULE'),
  ('ssh_tunnel_manager',
   '/Users/<USER>/PycharmProjects/every_day_statistices/ssh_tunnel_manager.py',
   'PYMODULE'),
  ('config',
   '/Users/<USER>/PycharmProjects/every_day_statistices/config.py',
   'PYMODULE'),
  ('dotenv',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dotenv/__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dotenv/ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dotenv/main.py',
   'PYMODULE'),
  ('dotenv.variables',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dotenv/variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dotenv/parser.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil._common',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('six',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/six.py',
   'PYMODULE'),
  ('dateutil',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydatetime.py',
   'PYMODULE')],
 [('Python.framework/Versions/3.13/Python',
   '/Library/Frameworks/Python.framework/Versions/3.13/Python',
   'BINARY'),
  ('lib-dynload/_csv.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/grp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_lzma.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bz2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_statistics.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_contextvars.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_decimal.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_pickle.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_hashlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_blake2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_md5.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha1.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_random.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bisect.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/math.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/array.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/select.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_socket.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/unicodedata.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/binascii.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_opcode.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/resource.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/syslog.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/fcntl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_ssl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_queue.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/mmap.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_posixshmem.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_ctypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_multiprocessing.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/pyexpat.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_scproxy.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/termios.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_asyncio.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_curses.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/readline.cpython-313-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-313-darwin.so',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/_cffi_backend.cpython-313-darwin.so',
   'EXTENSION'),
  ('bcrypt/_bcrypt.abi3.so',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bcrypt/_bcrypt.abi3.so',
   'EXTENSION'),
  ('cryptography/hazmat/bindings/_rust.abi3.so',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/_rust.abi3.so',
   'EXTENSION'),
  ('nacl/_sodium.abi3.so',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/_sodium.abi3.so',
   'EXTENSION'),
  ('bson/_cbson.cpython-313-darwin.so',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/bson/_cbson.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_uuid.cpython-313-darwin.so',
   'EXTENSION'),
  ('pymongo/_cmessage.cpython-313-darwin.so',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/pymongo/_cmessage.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_heapq.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_multibytecodec.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_jp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_kr.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_cn.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_tw.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_hk.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('libcrypto.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libssl.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/libssl.3.dylib',
   'BINARY')],
 [],
 [],
 [('.env.example',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.env.example',
   'DATA'),
  ('.env.ssh_template',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.env.ssh_template',
   'DATA'),
  ('BATCH_EXPORT_GUIDE.md',
   '/Users/<USER>/PycharmProjects/every_day_statistices/BATCH_EXPORT_GUIDE.md',
   'DATA'),
  ('README.md',
   '/Users/<USER>/PycharmProjects/every_day_statistices/README.md',
   'DATA'),
  ('SSH_TUNNEL_SETUP.md',
   '/Users/<USER>/PycharmProjects/every_day_statistices/SSH_TUNNEL_SETUP.md',
   'DATA'),
  ('batch_export_csv.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/batch_export_csv.sh',
   'DATA'),
  ('export_daily_stats.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/export_daily_stats.sh',
   'DATA'),
  ('install.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/install.sh',
   'DATA'),
  ('package_for_colleagues.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/package_for_colleagues.sh',
   'DATA'),
  ('quick_start.sh',
   '/Users/<USER>/PycharmProjects/every_day_statistices/quick_start.sh',
   'DATA'),
  ('requirements.txt',
   '/Users/<USER>/PycharmProjects/every_day_statistices/requirements.txt',
   'DATA'),
  ('nacl/py.typed',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/nacl/py.typed',
   'DATA'),
  ('cryptography-45.0.4.dist-info/METADATA',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/METADATA',
   'DATA'),
  ('cryptography-45.0.4.dist-info/RECORD',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/RECORD',
   'DATA'),
  ('cryptography-45.0.4.dist-info/licenses/LICENSE.BSD',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/licenses/LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.4.dist-info/WHEEL',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info/INSTALLER',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/INSTALLER',
   'DATA'),
  ('cryptography-45.0.4.dist-info/licenses/LICENSE.APACHE',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/licenses/LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.4.dist-info/licenses/LICENSE',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/licenses/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/Users/<USER>/PycharmProjects/every_day_statistices/.venv/lib/python3.13/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.13/Python', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/PycharmProjects/every_day_statistices/build/cdn_tool/base_library.zip',
   'DATA'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.13/Resources/Info.plist',
   '/Library/Frameworks/Python.framework/Versions/3.13/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.13', 'SYMLINK')],
 [('types',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/types.py',
   'PYMODULE'),
  ('operator',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/operator.py',
   'PYMODULE'),
  ('heapq',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/heapq.py',
   'PYMODULE'),
  ('stat',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/stat.py',
   'PYMODULE'),
  ('io',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/io.py',
   'PYMODULE'),
  ('weakref',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/weakref.py',
   'PYMODULE'),
  ('sre_compile',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sre_compile.py',
   'PYMODULE'),
  ('locale',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/locale.py',
   'PYMODULE'),
  ('reprlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/reprlib.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/idna.py',
   'PYMODULE'),
  ('encodings.hz',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/aliases.py',
   'PYMODULE'),
  ('encodings',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/__init__.py',
   'PYMODULE'),
  ('enum',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/enum.py',
   'PYMODULE'),
  ('abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/abc.py',
   'PYMODULE'),
  ('functools',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/functools.py',
   'PYMODULE'),
  ('codecs',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/codecs.py',
   'PYMODULE'),
  ('collections',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/collections/__init__.py',
   'PYMODULE'),
  ('copyreg',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/copyreg.py',
   'PYMODULE'),
  ('sre_parse',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sre_parse.py',
   'PYMODULE'),
  ('warnings',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/warnings.py',
   'PYMODULE'),
  ('_collections_abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_collections_abc.py',
   'PYMODULE'),
  ('posixpath',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/posixpath.py',
   'PYMODULE'),
  ('sre_constants',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sre_constants.py',
   'PYMODULE'),
  ('re._parser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_parser.py',
   'PYMODULE'),
  ('re._constants',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_constants.py',
   'PYMODULE'),
  ('re._compiler',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_casefix.py',
   'PYMODULE'),
  ('re',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/__init__.py',
   'PYMODULE'),
  ('genericpath',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/genericpath.py',
   'PYMODULE'),
  ('ntpath',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ntpath.py',
   'PYMODULE'),
  ('linecache',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/linecache.py',
   'PYMODULE'),
  ('_weakrefset',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_weakrefset.py',
   'PYMODULE'),
  ('keyword',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/keyword.py',
   'PYMODULE'),
  ('traceback',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/traceback.py',
   'PYMODULE'),
  ('os',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/os.py',
   'PYMODULE')])
