#!/bin/bash

# 快速启动脚本 - CDN统计数据批量导出
# 从2025-05-20到昨天的数据

echo "🚀 CDN统计数据批量导出 - 快速启动"
echo "=================================="
echo ""

# 检查Python脚本是否存在
if [ ! -f "main.py" ]; then
    echo "❌ 错误: main.py 文件不存在"
    echo "请确保在正确的项目目录中运行此脚本"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo "❌ 错误: .env 配置文件不存在"
    echo "请先配置数据库连接信息"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 显示选项菜单
echo "请选择导出方式:"
echo "1) Python脚本 (推荐) - 功能完整，错误处理好"
echo "2) Shell脚本 (简化版) - 快速简单"
echo "3) Shell脚本 (完整版) - 功能丰富"
echo "4) 自定义参数导出"
echo "5) 测试连接"
echo ""

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo ""
        echo "🐍 使用Python脚本导出..."
        python batch_export.py
        ;;
    2)
        echo ""
        echo "🐚 使用Shell脚本(简化版)导出..."
        ./export_daily_stats.sh
        ;;
    3)
        echo ""
        echo "🐚 使用Shell脚本(完整版)导出..."
        ./batch_export_csv.sh
        ;;
    4)
        echo ""
        echo "⚙️  自定义参数导出"
        echo ""
        read -p "开始日期 (YYYY-MM-DD, 默认: 2025-05-20): " start_date
        start_date=${start_date:-2025-05-20}
        
        read -p "结束日期 (YYYY-MM-DD, 默认: 昨天): " end_date
        
        read -p "输出目录 (默认: 自动生成): " output_dir
        
        echo ""
        echo "🐍 使用Python脚本导出..."
        
        cmd="python batch_export.py -s $start_date"
        if [ ! -z "$end_date" ]; then
            cmd="$cmd -e $end_date"
        fi
        if [ ! -z "$output_dir" ]; then
            cmd="$cmd -o $output_dir"
        fi
        
        echo "执行命令: $cmd"
        eval $cmd
        ;;
    5)
        echo ""
        echo "🔧 测试数据库连接..."
        echo ""
        echo "测试SSH隧道..."
        python main.py test-ssh
        echo ""
        echo "测试MongoDB连接..."
        python main.py test-connection
        echo ""
        echo "测试MySQL连接..."
        python main.py test-mysql
        ;;
    *)
        echo ""
        echo "❌ 无效选择，退出"
        exit 1
        ;;
esac

echo ""
echo "🎉 操作完成！"
