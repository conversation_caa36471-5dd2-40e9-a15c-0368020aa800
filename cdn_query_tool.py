from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pymongo
from pymongo import MongoClient
from dateutil import tz
from config import Config


class CDNQueryTool:
    """CDN统计数据查询工具"""
    
    def __init__(self):
        """初始化数据库连接"""
        self.config = Config.get_mongodb_config()
        self.client = None
        self.db = None
        self.collection = None
        self._connect()
    
    def _connect(self):
        """连接到MongoDB"""
        try:
            self.client = MongoClient(self.config['uri'])
            self.db = self.client[self.config['database']]
            self.collection = self.db[self.config['collection']]
            # 测试连接
            self.client.admin.command('ping')
            print(f"成功连接到MongoDB: {self.config['database']}")
        except Exception as e:
            print(f"连接MongoDB失败: {e}")
            raise
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()
    
    def convert_beijing_to_utc(self, beijing_time_str: str) -> datetime:
        """将北京时间字符串转换为UTC时间"""
        # 解析北京时间
        beijing_tz = tz.gettz('Asia/Shanghai')
        utc_tz = tz.gettz('UTC')
        
        # 解析时间字符串
        beijing_time = datetime.strptime(beijing_time_str, '%Y-%m-%d %H:%M:%S')
        beijing_time = beijing_time.replace(tzinfo=beijing_tz)
        
        # 转换为UTC
        utc_time = beijing_time.astimezone(utc_tz)
        return utc_time.replace(tzinfo=None)
    
    def query_flux_by_date_range(self, 
                                start_date: str, 
                                end_date: str = None,
                                metric_type: str = "access",
                                limit: int = None) -> List[Dict]:
        """
        查询指定日期范围的流量统计
        
        Args:
            start_date: 开始日期，格式：'2025-05-20' 或 '2025-05-20 00:00:00'
            end_date: 结束日期，格式同上。如果不提供，则查询单天数据
            metric_type: 指标类型，默认为 "access"
            limit: 限制返回结果数量
            
        Returns:
            查询结果列表
        """
        try:
            # 处理日期格式
            if len(start_date) == 10:  # 只有日期，没有时间
                start_datetime_str = f"{start_date} 00:00:00"
            else:
                start_datetime_str = start_date
            
            if end_date is None:
                # 如果没有提供结束日期，则查询单天数据
                start_dt = datetime.strptime(start_date.split()[0], '%Y-%m-%d')
                end_dt = start_dt + timedelta(days=1)
                end_datetime_str = end_dt.strftime('%Y-%m-%d 00:00:00')
            else:
                if len(end_date) == 10:
                    end_datetime_str = f"{end_date} 23:59:59"
                else:
                    end_datetime_str = end_date
            
            # 转换为UTC时间
            start_utc = self.convert_beijing_to_utc(start_datetime_str)
            end_utc = self.convert_beijing_to_utc(end_datetime_str)
            
            print(f"查询时间范围 (UTC): {start_utc} 到 {end_utc}")
            
            # 构建聚合管道
            pipeline = [
                {
                    "$match": {
                        "metric_type": metric_type,
                        "statistics_time": {
                            "$gte": start_utc,
                            "$lt": end_utc
                        }
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id",
                        "total_flux": {"$sum": "$flux"}
                    }
                },
                {
                    "$sort": {"total_flux": -1}
                }
            ]
            
            # 如果有限制，添加limit阶段
            if limit:
                pipeline.append({"$limit": limit})
            
            # 执行查询
            results = list(self.collection.aggregate(pipeline))
            
            print(f"查询完成，共找到 {len(results)} 条记录")
            return results
            
        except Exception as e:
            print(f"查询失败: {e}")
            return []
    
    def format_results(self, results: List[Dict], show_readable_size: bool = True) -> List[Dict]:
        """
        格式化查询结果
        
        Args:
            results: 查询结果
            show_readable_size: 是否显示可读的文件大小格式
            
        Returns:
            格式化后的结果
        """
        formatted_results = []
        
        for i, result in enumerate(results, 1):
            user_id = result['_id']
            total_flux = result['total_flux']
            
            formatted_result = {
                '排名': i,
                '用户ID': user_id,
                '总流量(字节)': total_flux
            }
            
            if show_readable_size:
                formatted_result['总流量(可读)'] = self._format_bytes(total_flux)
            
            formatted_results.append(formatted_result)
        
        return formatted_results
    
    def _format_bytes(self, bytes_value: int) -> str:
        """将字节数转换为可读格式"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB', 'PB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} EB"
    
    def export_to_csv(self, results: List[Dict], filename: str):
        """导出结果到CSV文件"""
        import csv
        
        if not results:
            print("没有数据可导出")
            return
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = results[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    writer.writerow(result)
            
            print(f"结果已导出到: {filename}")
        except Exception as e:
            print(f"导出失败: {e}")
