from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pymongo
from pymongo import MongoClient
from dateutil import tz
from config import Config
from ssh_tunnel_manager import SSHTunnelManager
from mysql_manager import MySQLManager


class CDNQueryTool:
    """CDN统计数据查询工具"""

    def __init__(self):
        """初始化数据库连接"""
        self.config = Config.get_mongodb_config()
        self.ssh_config = Config.get_ssh_config()
        self.mysql_config = Config.get_mysql_config()
        self.client = None
        self.db = None
        self.collection = None
        self.tunnel_manager = None
        self.mysql_manager = None
        self._connect()

    def _connect(self):
        """连接到MongoDB和MySQL"""
        try:
            # 连接MongoDB
            if self.ssh_config['use_ssh_tunnel']:
                print("启用SSH隧道模式")
                self.tunnel_manager = SSHTunnelManager()
                if not self.tunnel_manager.create_tunnel():
                    raise Exception("无法创建SSH隧道")

                # 使用隧道的本地端口连接
                mongodb_uri = self.tunnel_manager.get_local_mongodb_uri()
                print(f"通过SSH隧道连接MongoDB: {mongodb_uri}")
            else:
                # 直接连接
                mongodb_uri = self.config['uri']
                print(f"直接连接MongoDB: {mongodb_uri}")

            self.client = MongoClient(mongodb_uri)
            self.db = self.client[self.config['database']]
            self.collection = self.db[self.config['collection']]

            # 测试连接
            self.client.admin.command('ping')
            print(f"成功连接到MongoDB数据库: {self.config['database']}")

            # 连接MySQL（如果启用）
            if self.mysql_config['enable_lookup']:
                print("启用用户email查询功能")
                self.mysql_manager = MySQLManager(tunnel_manager=self.tunnel_manager)

        except Exception as e:
            print(f"连接数据库失败: {e}")
            self.close_connection()
            raise

    def close_connection(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()
            print("MongoDB连接已关闭")

        if self.mysql_manager:
            self.mysql_manager.close_connection()
            self.mysql_manager = None

        if self.tunnel_manager:
            self.tunnel_manager.close_tunnel()
            self.tunnel_manager = None

    def convert_beijing_to_utc(self, beijing_time_str: str) -> datetime:
        """将北京时间字符串转换为UTC时间"""
        # 解析北京时间
        beijing_tz = tz.gettz('Asia/Shanghai')
        utc_tz = tz.gettz('UTC')

        # 解析时间字符串
        beijing_time = datetime.strptime(beijing_time_str, '%Y-%m-%d %H:%M:%S')
        beijing_time = beijing_time.replace(tzinfo=beijing_tz)

        # 转换为UTC
        utc_time = beijing_time.astimezone(utc_tz)
        return utc_time.replace(tzinfo=None)

    def query_flux_by_date_range(self,
                                start_date: str,
                                end_date: str = None,
                                metric_type: str = "access",
                                limit: int = None) -> List[Dict]:
        """
        查询指定日期范围的流量统计

        Args:
            start_date: 开始日期，格式：'2025-05-20' 或 '2025-05-20 00:00:00'
            end_date: 结束日期，格式同上。如果不提供，则查询单天数据
            metric_type: 指标类型，默认为 "access"
            limit: 限制返回结果数量

        Returns:
            查询结果列表
        """
        try:
            # 处理日期格式
            if len(start_date) == 10:  # 只有日期，没有时间
                start_datetime_str = f"{start_date} 00:00:00"
            else:
                start_datetime_str = start_date

            if end_date is None:
                # 如果没有提供结束日期，则查询单天数据
                start_dt = datetime.strptime(start_date.split()[0], '%Y-%m-%d')
                end_dt = start_dt + timedelta(days=1)
                end_datetime_str = end_dt.strftime('%Y-%m-%d 00:00:00')
            else:
                if len(end_date) == 10:
                    end_datetime_str = f"{end_date} 23:59:59"
                else:
                    end_datetime_str = end_date

            # 转换为UTC时间
            start_utc = self.convert_beijing_to_utc(start_datetime_str)
            end_utc = self.convert_beijing_to_utc(end_datetime_str)

            print(f"查询时间范围 (UTC): {start_utc} 到 {end_utc}")

            # 构建聚合管道
            pipeline = [
                {
                    "$match": {
                        "metric_type": metric_type,
                        "statistics_time": {
                            "$gte": start_utc,
                            "$lt": end_utc
                        }
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id",
                        "total_flux": {"$sum": "$flux"}
                    }
                },
                {
                    "$sort": {"total_flux": -1}
                }
            ]

            # 如果有限制，添加limit阶段
            if limit:
                pipeline.append({"$limit": limit})

            # 执行查询
            results = list(self.collection.aggregate(pipeline))

            print(f"查询完成，共找到 {len(results)} 条记录")
            return results

        except Exception as e:
            print(f"查询失败: {e}")
            return []

    def format_results(self, results: List[Dict], show_readable_size: bool = True, show_email: bool = True) -> List[Dict]:
        """
        格式化查询结果

        Args:
            results: 查询结果
            show_readable_size: 是否显示可读的文件大小格式
            show_email: 是否显示用户email

        Returns:
            格式化后的结果
        """
        formatted_results = []

        # 如果启用了email查询，批量获取用户email
        user_emails = {}
        if show_email and self.mysql_manager and self.mysql_config['enable_lookup']:
            # 确保用户ID转换为字符串，并处理科学计数法问题
            user_ids = []
            for result in results:
                user_id = result['_id']
                # 处理科学计数法问题
                if isinstance(user_id, (int, float)):
                    user_id_str = str(int(user_id))  # 转换为整数再转字符串
                else:
                    user_id_str = str(user_id)
                    # 如果字符串包含科学计数法，尝试转换
                    if 'e' in user_id_str.lower():
                        try:
                            user_id_str = str(int(float(user_id_str)))
                        except:
                            pass  # 如果转换失败，保持原字符串
                user_ids.append(user_id_str)

            print(f"正在查询 {len(user_ids)} 个用户的email信息...")
            print(f"用户ID示例: {user_ids[:3]}...")  # 显示前3个用户ID用于调试
            user_emails = self.mysql_manager.batch_get_user_emails(user_ids)
            print(f"成功获取 {len(user_emails)} 个用户的email信息")

        for i, result in enumerate(results, 1):
            user_id = result['_id']
            total_flux = result['total_flux']

            # 确保用户ID显示为字符串，并处理科学计数法
            if isinstance(user_id, (int, float)):
                user_id_str = str(int(user_id))
            else:
                user_id_str = str(user_id)
                if 'e' in user_id_str.lower():
                    try:
                        user_id_str = str(int(float(user_id_str)))
                    except:
                        pass

            formatted_result = {
                '排名': i,
                '用户ID': user_id_str,
                '总流量(字节)': total_flux
            }

            # 添加用户email
            if show_email and self.mysql_config['enable_lookup']:
                email = user_emails.get(user_id_str, '未找到')
                formatted_result['用户邮箱'] = email

            if show_readable_size:
                formatted_result['总流量(可读)'] = self._format_bytes(total_flux)

            formatted_results.append(formatted_result)

        return formatted_results

    def _format_bytes(self, bytes_value: int) -> str:
        """将字节数转换为可读格式"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB', 'PB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} EB"

    def export_to_csv(self, results: List[Dict], filename: str):
        """导出结果到CSV文件"""
        import csv

        if not results:
            print("没有数据可导出")
            return

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = results[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for result in results:
                    writer.writerow(result)

            print(f"结果已导出到: {filename}")
        except Exception as e:
            print(f"导出失败: {e}")
