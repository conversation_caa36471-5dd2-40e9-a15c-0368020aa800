# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件
datas = [
    ('.env.example', '.'),
    ('.env.ssh_template', '.'),
    ('README.md', '.'),
    ('BATCH_EXPORT_GUIDE.md', '.'),
    ('SSH_TUNNEL_SETUP.md', '.'),
    ('requirements.txt', '.'),
    ('*.sh', '.'),
]

# 收集隐藏导入
hiddenimports = [
    'pymongo',
    'pymysql',
    'paramiko',
    'sshtunnel',
    'click',
    'tabulate',
    'python-dotenv',
    'python-dateutil',
    'urllib3',
    'cryptography',
    'bcrypt',
    'nacl',
]

# 主程序分析
a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 批量导出工具分析
batch_a = Analysis(
    ['batch_export.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 配置向导分析
setup_a = Analysis(
    ['setup_wizard.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# SSH配置向导分析
ssh_setup_a = Analysis(
    ['configure_ssh_tunnel.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 合并所有分析
MERGE((a, 'main', 'main'), 
      (batch_a, 'batch_export', 'batch_export'),
      (setup_a, 'setup_wizard', 'setup_wizard'),
      (ssh_setup_a, 'configure_ssh_tunnel', 'configure_ssh_tunnel'))

# 主程序PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 主程序可执行文件
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='cdn-query',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# 批量导出工具可执行文件
batch_exe = EXE(
    PYZ(batch_a.pure, batch_a.zipped_data, cipher=None),
    batch_a.scripts,
    [],
    exclude_binaries=True,
    name='cdn-batch-export',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# 配置向导可执行文件
setup_exe = EXE(
    PYZ(setup_a.pure, setup_a.zipped_data, cipher=None),
    setup_a.scripts,
    [],
    exclude_binaries=True,
    name='cdn-setup',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# SSH配置向导可执行文件
ssh_setup_exe = EXE(
    PYZ(ssh_setup_a.pure, ssh_setup_a.zipped_data, cipher=None),
    ssh_setup_a.scripts,
    [],
    exclude_binaries=True,
    name='cdn-ssh-setup',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# 收集所有文件到dist目录
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    batch_exe,
    batch_a.binaries,
    batch_a.zipfiles,
    batch_a.datas,
    setup_exe,
    setup_a.binaries,
    setup_a.zipfiles,
    setup_a.datas,
    ssh_setup_exe,
    ssh_setup_a.binaries,
    ssh_setup_a.zipfiles,
    ssh_setup_a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='cdn-stats-tool',
)
