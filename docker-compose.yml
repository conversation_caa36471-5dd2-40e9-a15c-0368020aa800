version: '3.8'

services:
  cdn-stats-bot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cdn-stats-telegram-bot
    restart: unless-stopped
    
    # 环境变量
    environment:
      - START_MODE=bot
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Shanghai
    
    # 挂载配置文件和数据目录
    volumes:
      - ./.env:/app/.env:ro
      - ./.env.bot:/app/.env.bot:ro
      - ./logs:/app/logs
      - ./exports:/app/exports
      - ./temp:/app/temp
      # 如果使用SSH密钥，挂载SSH目录
      # - ~/.ssh:/root/.ssh:ro
    
    # 网络配置
    networks:
      - cdn-network
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 可选：添加一个用于命令行操作的服务
  cdn-stats-cli:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cdn-stats-cli
    
    environment:
      - START_MODE=cli
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Shanghai
    
    volumes:
      - ./.env:/app/.env:ro
      - ./exports:/app/exports
      - ./temp:/app/temp
    
    networks:
      - cdn-network
    
    # 默认不启动，需要手动运行
    profiles:
      - cli
    
    # 交互式模式
    stdin_open: true
    tty: true

networks:
  cdn-network:
    driver: bridge

volumes:
  logs:
  exports:
  temp:
