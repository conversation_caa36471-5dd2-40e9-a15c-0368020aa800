# SSH隧道模式配置指南

## 概述

您的MongoDB连接信息：
- **主机**: *************:27017
- **用户**: mongouser
- **认证数据库**: admin
- **连接方式**: 通过SSH隧道

## 快速配置

### 方式1: 使用配置向导（推荐）

```bash
# 运行SSH隧道配置向导
python configure_ssh_tunnel.py setup
```

### 方式2: 手动配置

1. 复制模板文件：
```bash
cp .env.ssh_template .env
```

2. 编辑 `.env` 文件，填入以下信息：

```env
# SSH服务器信息
SSH_HOST=your_ssh_server_host
SSH_USERNAME=your_ssh_username
SSH_PASSWORD=your_ssh_password  # 或使用SSH_KEY_FILE

# MongoDB密码
REMOTE_MONGODB_PASSWORD=your_mongodb_password

# 数据库信息
DATABASE_NAME=your_actual_database_name
```

## 配置详解

### SSH隧道配置

```env
USE_SSH_TUNNEL=true
SSH_HOST=your_ssh_server_host      # SSH服务器地址
SSH_PORT=22                        # SSH端口
SSH_USERNAME=your_ssh_username     # SSH用户名

# 认证方式选择其一：
SSH_PASSWORD=your_ssh_password     # 方式1：密码认证
SSH_KEY_FILE=/path/to/private/key  # 方式2：密钥认证（推荐）
```

### MongoDB配置

```env
# 远程MongoDB信息（通过SSH隧道访问）
REMOTE_MONGODB_HOST=*************
REMOTE_MONGODB_PORT=27017
REMOTE_MONGODB_USERNAME=mongouser
REMOTE_MONGODB_PASSWORD=your_mongodb_password
REMOTE_MONGODB_AUTH_SOURCE=admin

# 本地隧道端口
LOCAL_TUNNEL_PORT=27018
```

### 工作原理

```
您的电脑 -> SSH隧道 -> SSH服务器 -> MongoDB(*************:27017)
    |                                           |
本地端口27018 <------ 自动创建的隧道 --------> mongouser@admin

您的电脑 -> SSH隧道 -> SSH服务器 -> MySQL(************:3306)
    |                                           |
本地端口3307 <------- 自动创建的隧道 --------> readonly_cs@cloud
```

## 测试连接

### 1. 测试SSH隧道

```bash
python main.py test-ssh
```

预期输出：
```
正在创建MongoDB SSH隧道到 your_ssh_server:22
MongoDB SSH隧道创建成功! 本地端口: 27018
正在创建MySQL SSH隧道到 your_ssh_server:22
MySQL SSH隧道创建成功! 本地端口: 3307
```

### 2. 测试MongoDB连接

```bash
python main.py test-connection
```

### 3. 测试完整功能

```bash
# 查询数据
python main.py query -s "2025-05-20" -l 5
```

## 常见问题

### 1. SSH连接失败

**问题**: `SSH连接失败: Authentication failed`

**解决方案**:
- 检查SSH用户名和密码
- 确认SSH服务器地址和端口
- 如使用密钥，检查密钥文件路径和权限

### 2. MongoDB认证失败

**问题**: `MongoDB连接失败: Authentication failed`

**解决方案**:
- 检查MongoDB用户名密码
- 确认认证数据库设置（authSource=admin）
- 验证MongoDB服务是否运行

### 3. 端口冲突

**问题**: `Address already in use`

**解决方案**:
- 修改 `LOCAL_TUNNEL_PORT` 为其他端口
- 检查是否有其他程序占用端口

## 手动SSH隧道（调试用）

如果自动隧道有问题，可以手动创建隧道进行调试：

```bash
# 创建MongoDB隧道
ssh -L 27018:*************:27017 username@ssh_server

# 创建MySQL隧道（在另一个终端）
ssh -L 3307:************:3306 username@ssh_server

# 或者在一个命令中创建多个隧道
ssh -L 27018:*************:27017 -L 3307:************:3306 username@ssh_server
```

然后在另一个终端测试连接：
```bash
# 测试MongoDB连接
mongo **************************************************************

# 测试MySQL连接
mysql -h localhost -P 3307 -u readonly_cs -p cloud
```

## 安全建议

1. **使用SSH密钥认证**而不是密码
2. **设置.env文件权限**：`chmod 600 .env`
3. **不要将.env文件提交到版本控制**
4. **定期更换密码**

## 配置示例

完整的 `.env` 配置示例：

```env
# MongoDB配置
MONGODB_URI=mongodb://localhost:27017/
DATABASE_NAME=cdn_stats
COLLECTION_NAME=cdn_statistics_total_metric

# SSH隧道配置
USE_SSH_TUNNEL=true
SSH_HOST=jump.example.com
SSH_PORT=22
SSH_USERNAME=admin
SSH_KEY_FILE=/Users/<USER>/.ssh/id_rsa

# 远程MongoDB配置
REMOTE_MONGODB_HOST=*************
REMOTE_MONGODB_PORT=27017
REMOTE_MONGODB_USERNAME=mongouser
REMOTE_MONGODB_PASSWORD=your_secure_password
REMOTE_MONGODB_AUTH_SOURCE=admin

# 本地隧道端口
LOCAL_TUNNEL_PORT=27018

# MySQL配置（通过SSH隧道）
ENABLE_USER_EMAIL_LOOKUP=true
MYSQL_HOST=localhost
MYSQL_PORT=3307
MYSQL_USERNAME=readonly_cs
MYSQL_PASSWORD=mysql_password
MYSQL_DATABASE=cloud
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email

# 远程MySQL配置
REMOTE_MYSQL_HOST=************
REMOTE_MYSQL_PORT=3306

# 本地MySQL隧道端口
LOCAL_MYSQL_TUNNEL_PORT=3307

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai
```
