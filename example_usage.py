#!/usr/bin/env python3
"""
CDN查询工具使用示例
演示如何使用SSH隧道连接MongoDB并查询数据
"""

from cdn_query_tool import CDNQueryTool
from datetime import datetime, timedelta


def example_basic_query():
    """基本查询示例"""
    print("=== 基本查询示例 ===")
    
    tool = None
    try:
        # 创建查询工具实例
        tool = CDNQueryTool()
        
        # 查询昨天的数据
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        print(f"查询日期: {yesterday}")
        
        results = tool.query_flux_by_date_range(
            start_date=yesterday,
            limit=5  # 只显示前5条记录
        )
        
        if results:
            formatted_results = tool.format_results(results)
            print("\n查询结果:")
            for result in formatted_results:
                print(f"排名: {result['排名']}, "
                      f"用户ID: {result['用户ID']}, "
                      f"流量: {result['总流量(可读)']}")
        else:
            print("没有找到数据")
            
    except Exception as e:
        print(f"查询失败: {e}")
    finally:
        if tool:
            tool.close_connection()


def example_date_range_query():
    """日期范围查询示例"""
    print("\n=== 日期范围查询示例 ===")
    
    tool = None
    try:
        tool = CDNQueryTool()
        
        # 查询最近3天的数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
        
        print(f"查询时间范围: {start_date} 到 {end_date}")
        
        results = tool.query_flux_by_date_range(
            start_date=start_date,
            end_date=end_date,
            limit=10
        )
        
        if results:
            formatted_results = tool.format_results(results)
            
            # 计算统计信息
            total_users = len(results)
            total_flux = sum(result['total_flux'] for result in results)
            
            print(f"\n统计信息:")
            print(f"用户总数: {total_users}")
            print(f"总流量: {tool._format_bytes(total_flux)}")
            
            print(f"\n前10名用户:")
            for result in formatted_results[:10]:
                print(f"{result['排名']}. {result['用户ID']}: {result['总流量(可读)']}")
                
        else:
            print("没有找到数据")
            
    except Exception as e:
        print(f"查询失败: {e}")
    finally:
        if tool:
            tool.close_connection()


def example_export_csv():
    """导出CSV示例"""
    print("\n=== 导出CSV示例 ===")
    
    tool = None
    try:
        tool = CDNQueryTool()
        
        # 查询昨天的数据
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        results = tool.query_flux_by_date_range(
            start_date=yesterday,
            limit=100  # 导出前100条记录
        )
        
        if results:
            formatted_results = tool.format_results(results)
            
            # 导出到CSV
            filename = f"cdn_stats_{yesterday}.csv"
            tool.export_to_csv(formatted_results, filename)
            print(f"数据已导出到: {filename}")
        else:
            print("没有数据可导出")
            
    except Exception as e:
        print(f"导出失败: {e}")
    finally:
        if tool:
            tool.close_connection()


def main():
    """主函数"""
    print("CDN查询工具使用示例")
    print("=" * 50)
    
    try:
        # 运行示例
        example_basic_query()
        example_date_range_query()
        example_export_csv()
        
        print("\n✅ 所有示例执行完成")
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")


if __name__ == "__main__":
    main()
