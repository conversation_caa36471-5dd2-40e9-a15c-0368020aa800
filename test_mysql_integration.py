#!/usr/bin/env python3
"""
MySQL集成测试脚本
测试MySQL用户邮箱查询功能
"""

from mysql_manager import MySQLManager
from config import Config


def test_mysql_basic():
    """测试MySQL基本功能"""
    print("=== MySQL基本功能测试 ===")
    
    config = Config.get_mysql_config()
    
    if not config['enable_lookup']:
        print("❌ MySQL用户查询功能未启用")
        return False
    
    try:
        with MySQLManager() as mysql_manager:
            # 测试连接
            if not mysql_manager.test_connection():
                print("❌ MySQL连接测试失败")
                return False
            
            # 获取用户总数
            user_count = mysql_manager.get_user_count()
            print(f"✅ 用户表总记录数: {user_count}")
            
            return True
            
    except Exception as e:
        print(f"❌ MySQL测试失败: {e}")
        return False


def test_single_user_query():
    """测试单个用户查询"""
    print("\n=== 单个用户查询测试 ===")
    
    test_user_id = "2796484965376"  # 使用您提供的示例用户ID
    
    try:
        with MySQLManager() as mysql_manager:
            email = mysql_manager.get_user_email(test_user_id)
            
            if email:
                print(f"✅ 用户ID {test_user_id} 的邮箱: {email}")
                return True
            else:
                print(f"⚠️  未找到用户ID {test_user_id} 的邮箱信息")
                
                # 尝试查询前几个用户作为示例
                print("\n尝试查询前5个用户...")
                mysql_manager.connection.cursor().execute(f"""
                    SELECT {mysql_manager.config['user_id_field']}, 
                           {mysql_manager.config['user_email_field']} 
                    FROM {mysql_manager.config['user_table']} 
                    LIMIT 5
                """)
                results = mysql_manager.connection.cursor().fetchall()
                
                if results:
                    print("前5个用户示例:")
                    for row in results:
                        user_id = row[mysql_manager.config['user_id_field']]
                        email = row[mysql_manager.config['user_email_field']]
                        print(f"  用户ID: {user_id}, 邮箱: {email}")
                
                return False
                
    except Exception as e:
        print(f"❌ 单个用户查询失败: {e}")
        return False


def test_batch_user_query():
    """测试批量用户查询"""
    print("\n=== 批量用户查询测试 ===")
    
    # 模拟一些用户ID
    test_user_ids = [
        "2796484965376",
        "2796484965377", 
        "2796484965378",
        "nonexistent_user"  # 不存在的用户
    ]
    
    try:
        with MySQLManager() as mysql_manager:
            user_emails = mysql_manager.batch_get_user_emails(test_user_ids)
            
            print(f"查询 {len(test_user_ids)} 个用户ID")
            print(f"找到 {len(user_emails)} 个用户的邮箱信息")
            
            for user_id in test_user_ids:
                email = user_emails.get(user_id)
                if email:
                    print(f"✅ 用户ID {user_id}: {email}")
                else:
                    print(f"❌ 用户ID {user_id}: 未找到")
            
            return len(user_emails) > 0
            
    except Exception as e:
        print(f"❌ 批量用户查询失败: {e}")
        return False


def test_cache_functionality():
    """测试缓存功能"""
    print("\n=== 缓存功能测试 ===")
    
    test_user_id = "2796484965376"
    
    try:
        with MySQLManager() as mysql_manager:
            # 第一次查询
            print("第一次查询（从数据库）...")
            email1 = mysql_manager.get_user_email(test_user_id)
            
            # 第二次查询（从缓存）
            print("第二次查询（从缓存）...")
            email2 = mysql_manager.get_user_email(test_user_id)
            
            if email1 == email2:
                print(f"✅ 缓存功能正常，邮箱: {email1}")
                
                # 检查缓存内容
                cache_size = len(mysql_manager.user_cache)
                print(f"缓存中有 {cache_size} 个用户信息")
                
                return True
            else:
                print("❌ 缓存功能异常")
                return False
                
    except Exception as e:
        print(f"❌ 缓存功能测试失败: {e}")
        return False


def test_integration_with_cdn_tool():
    """测试与CDN查询工具的集成"""
    print("\n=== CDN工具集成测试 ===")
    
    try:
        from cdn_query_tool import CDNQueryTool
        
        # 模拟查询结果
        mock_results = [
            {"_id": "2796484965376", "total_flux": 1073741824},
            {"_id": "2796484965377", "total_flux": 536870912},
            {"_id": "nonexistent_user", "total_flux": 268435456}
        ]
        
        print("创建CDN查询工具实例...")
        tool = CDNQueryTool()
        
        print("格式化模拟结果（包含邮箱查询）...")
        formatted_results = tool.format_results(mock_results, show_email=True)
        
        print("格式化结果:")
        for result in formatted_results:
            print(f"  排名: {result['排名']}")
            print(f"  用户ID: {result['用户ID']}")
            if '用户邮箱' in result:
                print(f"  用户邮箱: {result['用户邮箱']}")
            print(f"  总流量: {result['总流量(可读)']}")
            print()
        
        tool.close_connection()
        return True
        
    except Exception as e:
        print(f"❌ CDN工具集成测试失败: {e}")
        return False


def main():
    """主函数"""
    print("MySQL集成测试")
    print("=" * 50)
    
    tests = [
        ("MySQL基本功能", test_mysql_basic),
        ("单个用户查询", test_single_user_query),
        ("批量用户查询", test_batch_user_query),
        ("缓存功能", test_cache_functionality),
        ("CDN工具集成", test_integration_with_cdn_tool)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查配置")


if __name__ == "__main__":
    main()
