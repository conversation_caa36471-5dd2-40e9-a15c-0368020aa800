#!/usr/bin/env python3
"""
CDN统计查询工具打包脚本
支持多种打包方式
"""

import os
import sys
import shutil
import subprocess
import zipfile
import tarfile
from pathlib import Path
import click


def check_requirements():
    """检查打包环境"""
    print("🔍 检查打包环境...")

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本需要3.8或更高")
        return False

    print(f"✅ Python版本: {sys.version}")

    # 检查必要的包
    required_packages = {
        'pyinstaller': 'PyInstaller',
        'setuptools': 'setuptools',
        'wheel': 'wheel'
    }
    missing_packages = []

    for package_name, import_name in required_packages.items():
        try:
            # 先尝试导入
            __import__(import_name)
            print(f"✅ {package_name}: 已安装")
        except ImportError:
            # 如果导入失败，尝试用subprocess检查
            try:
                result = subprocess.run([sys.executable, '-m', 'pip', 'show', package_name],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ {package_name}: 已安装 (通过pip检测)")
                else:
                    missing_packages.append(package_name)
                    print(f"❌ {package_name}: 未安装")
            except:
                missing_packages.append(package_name)
                print(f"❌ {package_name}: 未安装")

    if missing_packages:
        print(f"\n请安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    return True


def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")

    dirs_to_clean = ['build', 'dist', '*.egg-info', '__pycache__']

    for pattern in dirs_to_clean:
        if '*' in pattern:
            import glob
            for path in glob.glob(pattern):
                if os.path.isdir(path):
                    shutil.rmtree(path)
                    print(f"   删除目录: {path}")
        else:
            if os.path.exists(pattern):
                if os.path.isdir(pattern):
                    shutil.rmtree(pattern)
                else:
                    os.remove(pattern)
                print(f"   删除: {pattern}")


def build_pyinstaller():
    """使用PyInstaller打包"""
    print("📦 使用PyInstaller打包...")

    try:
        # 使用spec文件打包
        cmd = ['pyinstaller', '--clean', 'cdn_tool.spec']
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ PyInstaller打包成功")
            return True
        else:
            print(f"❌ PyInstaller打包失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ PyInstaller打包异常: {e}")
        return False


def build_wheel():
    """构建wheel包"""
    print("🎡 构建wheel包...")

    try:
        cmd = [sys.executable, 'setup.py', 'bdist_wheel']
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ wheel包构建成功")
            return True
        else:
            print(f"❌ wheel包构建失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ wheel包构建异常: {e}")
        return False


def build_source_dist():
    """构建源码分发包"""
    print("📄 构建源码分发包...")

    try:
        cmd = [sys.executable, 'setup.py', 'sdist']
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 源码分发包构建成功")
            return True
        else:
            print(f"❌ 源码分发包构建失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 源码分发包构建异常: {e}")
        return False


def create_portable_package():
    """创建便携版包"""
    print("💼 创建便携版包...")

    portable_dir = "cdn-stats-tool-portable"

    try:
        # 创建便携版目录
        if os.path.exists(portable_dir):
            shutil.rmtree(portable_dir)
        os.makedirs(portable_dir)

        # 复制Python文件
        python_files = [
            'main.py', 'cdn_query_tool.py', 'mysql_manager.py',
            'ssh_tunnel_manager.py', 'config.py', 'batch_export.py',
            'setup_wizard.py', 'configure_ssh_tunnel.py',
            'requirements.txt', '.env.example', '.env.ssh_template'
        ]

        for file in python_files:
            if os.path.exists(file):
                shutil.copy2(file, portable_dir)

        # 复制文档文件
        doc_files = [
            'README.md', 'BATCH_EXPORT_GUIDE.md', 'SSH_TUNNEL_SETUP.md'
        ]

        for file in doc_files:
            if os.path.exists(file):
                shutil.copy2(file, portable_dir)

        # 复制shell脚本
        shell_files = [
            'batch_export_csv.sh', 'export_daily_stats.sh', 'quick_start.sh'
        ]

        for file in shell_files:
            if os.path.exists(file):
                shutil.copy2(file, portable_dir)
                # 设置执行权限
                os.chmod(os.path.join(portable_dir, file), 0o755)

        # 创建启动脚本
        create_launcher_scripts(portable_dir)

        print("✅ 便携版包创建成功")
        return True

    except Exception as e:
        print(f"❌ 便携版包创建失败: {e}")
        return False


def create_launcher_scripts(portable_dir):
    """创建启动脚本"""

    # Windows批处理文件
    bat_content = """@echo off
echo CDN统计查询工具
echo ================
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 安装依赖
echo 正在安装依赖包...
pip install -r requirements.txt

REM 运行主程序
echo.
echo 启动CDN查询工具...
python main.py %*

pause
"""

    with open(os.path.join(portable_dir, 'start.bat'), 'w', encoding='utf-8') as f:
        f.write(bat_content)

    # Linux/macOS shell脚本
    sh_content = """#!/bin/bash

echo "CDN统计查询工具"
echo "================"
echo ""

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8或更高版本"
    exit 1
fi

# 安装依赖
echo "正在安装依赖包..."
pip3 install -r requirements.txt

# 运行主程序
echo ""
echo "启动CDN查询工具..."
python3 main.py "$@"
"""

    sh_file = os.path.join(portable_dir, 'start.sh')
    with open(sh_file, 'w', encoding='utf-8') as f:
        f.write(sh_content)
    os.chmod(sh_file, 0o755)

    # 创建README
    readme_content = """# CDN统计查询工具 - 便携版

## 快速开始

### Windows用户
双击运行 `start.bat`

### Linux/macOS用户
```bash
./start.sh
```

## 手动运行

1. 安装依赖:
```bash
pip install -r requirements.txt
```

2. 配置数据库连接:
```bash
python setup_wizard.py setup
```

3. 运行工具:
```bash
python main.py query -s 2025-05-20
```

## 批量导出

```bash
python batch_export.py
```

## 更多帮助

查看完整文档: README.md
"""

    with open(os.path.join(portable_dir, 'README_PORTABLE.md'), 'w', encoding='utf-8') as f:
        f.write(readme_content)


def create_archives():
    """创建压缩包"""
    print("📦 创建压缩包...")

    archives_created = []

    # 创建便携版压缩包
    portable_dir = "cdn-stats-tool-portable"
    if os.path.exists(portable_dir):
        # ZIP格式
        zip_name = f"{portable_dir}.zip"
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(portable_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, '.')
                    zipf.write(file_path, arcname)
        archives_created.append(zip_name)
        print(f"✅ 创建ZIP包: {zip_name}")

        # TAR.GZ格式
        tar_name = f"{portable_dir}.tar.gz"
        with tarfile.open(tar_name, 'w:gz') as tarf:
            tarf.add(portable_dir, arcname=portable_dir)
        archives_created.append(tar_name)
        print(f"✅ 创建TAR.GZ包: {tar_name}")

    # 创建PyInstaller二进制包
    if os.path.exists('dist/cdn-stats-tool'):
        binary_zip = "cdn-stats-tool-binary.zip"
        with zipfile.ZipFile(binary_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk('dist/cdn-stats-tool'):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, 'dist')
                    zipf.write(file_path, arcname)
        archives_created.append(binary_zip)
        print(f"✅ 创建二进制包: {binary_zip}")

    return archives_created


def show_package_info():
    """显示打包结果信息"""
    print("\n" + "="*50)
    print("📦 打包完成！")
    print("="*50)

    print("\n📁 生成的文件:")

    # 检查各种包
    if os.path.exists('dist'):
        print("\n🔧 可执行文件 (dist/):")
        for item in os.listdir('dist'):
            item_path = os.path.join('dist', item)
            if os.path.isdir(item_path):
                print(f"   📂 {item}/")
            else:
                print(f"   📄 {item}")

    if os.path.exists('cdn-stats-tool-portable'):
        print("\n💼 便携版 (cdn-stats-tool-portable/):")
        print("   包含所有源码和启动脚本")

    # 检查压缩包
    archives = [f for f in os.listdir('.') if f.endswith(('.zip', '.tar.gz')) and 'cdn-stats-tool' in f]
    if archives:
        print("\n📦 压缩包:")
        for archive in archives:
            size = os.path.getsize(archive) / (1024*1024)
            print(f"   📦 {archive} ({size:.1f} MB)")

    # 检查wheel包
    if os.path.exists('dist') and any(f.endswith('.whl') for f in os.listdir('dist')):
        print("\n🎡 Python包 (dist/):")
        for f in os.listdir('dist'):
            if f.endswith(('.whl', '.tar.gz')):
                print(f"   📦 {f}")

    print("\n💡 使用说明:")
    print("   - 可执行文件: 解压后直接运行")
    print("   - 便携版: 需要Python环境，运行start.bat/start.sh")
    print("   - Python包: pip install dist/*.whl")


@click.command()
@click.option('--type', '-t',
              type=click.Choice(['all', 'binary', 'portable', 'wheel', 'source']),
              default='all',
              help='打包类型')
@click.option('--clean', '-c', is_flag=True, help='清理构建目录')
def main(type, clean):
    """CDN统计查询工具打包脚本"""

    print("🚀 CDN统计查询工具打包程序")
    print("="*40)

    # 检查环境
    if not check_requirements():
        sys.exit(1)

    # 清理构建目录
    if clean:
        clean_build_dirs()

    success_count = 0
    total_count = 0

    # 根据类型执行打包
    if type in ['all', 'binary']:
        total_count += 1
        if build_pyinstaller():
            success_count += 1

    if type in ['all', 'portable']:
        total_count += 1
        if create_portable_package():
            success_count += 1

    if type in ['all', 'wheel']:
        total_count += 1
        if build_wheel():
            success_count += 1

    if type in ['all', 'source']:
        total_count += 1
        if build_source_dist():
            success_count += 1

    # 创建压缩包
    if type in ['all', 'portable', 'binary']:
        archives = create_archives()
        if archives:
            print(f"✅ 创建了 {len(archives)} 个压缩包")

    # 显示结果
    print(f"\n📊 打包结果: {success_count}/{total_count} 成功")

    if success_count > 0:
        show_package_info()
    else:
        print("❌ 所有打包都失败了")
        sys.exit(1)


if __name__ == "__main__":
    main()
