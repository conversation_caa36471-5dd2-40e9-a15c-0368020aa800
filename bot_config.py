#!/usr/bin/env python3
"""
Telegram机器人配置管理
"""

import os
from typing import List, Dict, Optional
from dataclasses import dataclass


@dataclass
class BotConfig:
    """机器人配置类"""
    
    # Telegram配置
    bot_token: str
    allowed_users: List[int]
    admin_users: List[int]
    
    # 功能配置
    max_query_days: int = 30  # 最大查询天数
    max_batch_days: int = 30  # 最大批量导出天数
    max_records_per_query: int = 1000  # 单次查询最大记录数
    
    # 文件配置
    max_file_size_mb: int = 50  # 最大文件大小(MB)
    temp_dir: str = "/tmp/cdn_bot"
    
    # 限流配置
    rate_limit_per_user: int = 10  # 每用户每分钟最大请求数
    
    @classmethod
    def from_env(cls) -> 'BotConfig':
        """从环境变量创建配置"""
        
        # 获取Bot Token
        bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            raise ValueError("TELEGRAM_BOT_TOKEN environment variable is required")
        
        # 获取允许的用户ID
        allowed_users_str = os.getenv('TELEGRAM_ALLOWED_USERS', '')
        if not allowed_users_str:
            raise ValueError("TELEGRAM_ALLOWED_USERS environment variable is required")
        
        try:
            allowed_users = [int(uid.strip()) for uid in allowed_users_str.split(',')]
        except ValueError:
            raise ValueError("TELEGRAM_ALLOWED_USERS must be comma-separated integers")
        
        # 获取管理员用户ID（可选）
        admin_users_str = os.getenv('TELEGRAM_ADMIN_USERS', '')
        admin_users = []
        if admin_users_str:
            try:
                admin_users = [int(uid.strip()) for uid in admin_users_str.split(',')]
            except ValueError:
                raise ValueError("TELEGRAM_ADMIN_USERS must be comma-separated integers")
        
        # 其他配置
        max_query_days = int(os.getenv('BOT_MAX_QUERY_DAYS', '30'))
        max_batch_days = int(os.getenv('BOT_MAX_BATCH_DAYS', '30'))
        max_records_per_query = int(os.getenv('BOT_MAX_RECORDS_PER_QUERY', '1000'))
        max_file_size_mb = int(os.getenv('BOT_MAX_FILE_SIZE_MB', '50'))
        temp_dir = os.getenv('BOT_TEMP_DIR', '/tmp/cdn_bot')
        rate_limit_per_user = int(os.getenv('BOT_RATE_LIMIT_PER_USER', '10'))
        
        return cls(
            bot_token=bot_token,
            allowed_users=allowed_users,
            admin_users=admin_users,
            max_query_days=max_query_days,
            max_batch_days=max_batch_days,
            max_records_per_query=max_records_per_query,
            max_file_size_mb=max_file_size_mb,
            temp_dir=temp_dir,
            rate_limit_per_user=rate_limit_per_user
        )
    
    def is_user_allowed(self, user_id: int) -> bool:
        """检查用户是否被允许"""
        return user_id in self.allowed_users
    
    def is_user_admin(self, user_id: int) -> bool:
        """检查用户是否是管理员"""
        return user_id in self.admin_users
    
    def get_user_role(self, user_id: int) -> str:
        """获取用户角色"""
        if self.is_user_admin(user_id):
            return "admin"
        elif self.is_user_allowed(user_id):
            return "user"
        else:
            return "unauthorized"


class RateLimiter:
    """简单的限流器"""
    
    def __init__(self, max_requests_per_minute: int = 10):
        self.max_requests = max_requests_per_minute
        self.requests: Dict[int, List[float]] = {}
    
    def is_allowed(self, user_id: int) -> bool:
        """检查用户是否允许发送请求"""
        import time
        
        current_time = time.time()
        
        # 清理过期的请求记录
        if user_id in self.requests:
            self.requests[user_id] = [
                req_time for req_time in self.requests[user_id]
                if current_time - req_time < 60  # 保留最近1分钟的记录
            ]
        else:
            self.requests[user_id] = []
        
        # 检查是否超过限制
        if len(self.requests[user_id]) >= self.max_requests:
            return False
        
        # 记录当前请求
        self.requests[user_id].append(current_time)
        return True
    
    def get_remaining_requests(self, user_id: int) -> int:
        """获取用户剩余请求数"""
        if user_id not in self.requests:
            return self.max_requests
        
        return max(0, self.max_requests - len(self.requests[user_id]))


def create_bot_env_template():
    """创建机器人环境变量模板"""
    template = """
# Telegram机器人配置模板
# 复制此文件为 .env.bot 并填入实际值

# ===========================================
# Telegram Bot 基本配置
# ===========================================

# Bot Token (从 @BotFather 获取)
TELEGRAM_BOT_TOKEN=your_bot_token_here

# 允许使用机器人的用户ID (逗号分隔)
# 获取用户ID: 发送消息给 @userinfobot
TELEGRAM_ALLOWED_USERS=123456789,987654321

# 管理员用户ID (可选，逗号分隔)
TELEGRAM_ADMIN_USERS=123456789

# ===========================================
# 机器人功能限制
# ===========================================

# 最大查询天数
BOT_MAX_QUERY_DAYS=30

# 最大批量导出天数
BOT_MAX_BATCH_DAYS=30

# 单次查询最大记录数
BOT_MAX_RECORDS_PER_QUERY=1000

# 最大文件大小(MB)
BOT_MAX_FILE_SIZE_MB=50

# 临时文件目录
BOT_TEMP_DIR=/tmp/cdn_bot

# 每用户每分钟最大请求数
BOT_RATE_LIMIT_PER_USER=10

# ===========================================
# 数据库配置 (继承自主配置文件)
# ===========================================
# 机器人会自动读取现有的数据库配置
# 确保 .env 文件中的数据库配置正确

# ===========================================
# 使用说明
# ===========================================
# 1. 创建Telegram机器人:
#    - 发送 /newbot 给 @BotFather
#    - 按提示设置机器人名称和用户名
#    - 获取Bot Token并填入上方
#
# 2. 获取用户ID:
#    - 发送任意消息给 @userinfobot
#    - 获取您的用户ID并填入上方
#
# 3. 启动机器人:
#    python telegram_bot.py
#
# 4. 测试机器人:
#    - 在Telegram中搜索您的机器人
#    - 发送 /start 开始使用
"""
    
    with open('.env.bot.example', 'w', encoding='utf-8') as f:
        f.write(template)
    
    print("✅ 机器人配置模板已创建: .env.bot.example")
    print("请复制为 .env.bot 并填入实际配置")


if __name__ == "__main__":
    create_bot_env_template()
