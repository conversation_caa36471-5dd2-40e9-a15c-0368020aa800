# MongoDB 配置（直接连接）
# 请将 YOUR_MONGODB_PASSWORD 替换为实际密码
MONGODB_URI=*******************************************************************************
DATABASE_NAME=your_database_name
COLLECTION_NAME=cdn_statistics_total_metric

# SSH 隧道配置（当前不使用）
USE_SSH_TUNNEL=false
SSH_HOST=
SSH_PORT=22
SSH_USERNAME=
SSH_PASSWORD=
SSH_KEY_FILE=

# 远程MongoDB配置（SSH隧道模式下使用）
REMOTE_MONGODB_HOST=localhost
REMOTE_MONGODB_PORT=27017

# 本地隧道端口
LOCAL_TUNNEL_PORT=27018

# MySQL 配置（用于查询用户email）
ENABLE_USER_EMAIL_LOOKUP=true
MYSQL_HOST=*************
MYSQL_PORT=3306
MYSQL_USERNAME=your_mysql_username
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=your_mysql_database
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai
