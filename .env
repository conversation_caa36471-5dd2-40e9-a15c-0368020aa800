# MongoDB 配置（通过SSH隧道连接）
# 注意：在SSH隧道模式下，MONGODB_URI 会被自动构建，这里的设置主要用于直连模式
MONGODB_URI=mongodb://localhost:27017/
DATABASE_NAME=maoyun
COLLECTION_NAME=cdn_statistics_total_metric

# SSH 隧道配置（启用）
USE_SSH_TUNNEL=true
SSH_HOST=*************
SSH_PORT=22
SSH_USERNAME=root
SSH_PASSWORD=1YxY6rQGV4dJ3xdgQFnm
# 或者使用SSH密钥文件（推荐，注释掉密码行）
# SSH_KEY_FILE=/path/to/your/ssh/private/key

# 远程MongoDB配置（通过SSH隧道访问的实际MongoDB）
# 这是SSH服务器上MongoDB的实际地址和认证信息
REMOTE_MONGODB_HOST=*************
REMOTE_MONGODB_PORT=27017
REMOTE_MONGODB_USERNAME=mongouser
REMOTE_MONGODB_PASSWORD=your_mongodb_password
REMOTE_MONGODB_AUTH_SOURCE=admin

# 本地隧道端口（工具会在本地创建此端口的隧道）
LOCAL_TUNNEL_PORT=27018

# MySQL 配置（用于查询用户email，通过SSH隧道）
ENABLE_USER_EMAIL_LOOKUP=true
MYSQL_HOST=localhost
MYSQL_PORT=3307
MYSQL_USERNAME=readonly_cs
MYSQL_PASSWORD=RZ83GA4X5saCaeQbFLDu
MYSQL_DATABASE=cloud
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email

# 远程MySQL配置（通过SSH隧道访问的实际MySQL）
REMOTE_MYSQL_HOST=************
REMOTE_MYSQL_PORT=3306

# 本地MySQL隧道端口
LOCAL_MYSQL_TUNNEL_PORT=3307

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai
