#!/usr/bin/env python3
"""
CDN查询工具测试脚本
"""

from cdn_query_tool import CDNQueryTool
from datetime import datetime


def test_time_conversion():
    """测试时间转换功能"""
    print("=== 测试时间转换功能 ===")
    
    tool = CDNQueryTool()
    
    # 测试北京时间转UTC
    beijing_time = "2025-05-20 00:00:00"
    utc_time = tool.convert_beijing_to_utc(beijing_time)
    
    print(f"北京时间: {beijing_time}")
    print(f"UTC时间: {utc_time}")
    print(f"预期UTC时间: 2025-05-19 16:00:00")
    
    tool.close_connection()


def test_format_bytes():
    """测试字节格式化功能"""
    print("\n=== 测试字节格式化功能 ===")
    
    tool = CDNQueryTool()
    
    test_values = [
        1024,
        1048576,
        1073741824,
        1099511627776
    ]
    
    for value in test_values:
        formatted = tool._format_bytes(value)
        print(f"{value} 字节 = {formatted}")
    
    tool.close_connection()


def test_query_simulation():
    """模拟查询测试（不实际连接数据库）"""
    print("\n=== 模拟查询结果格式化测试 ===")
    
    # 模拟查询结果
    mock_results = [
        {"_id": "user123", "total_flux": 1073741824},
        {"_id": "user456", "total_flux": 536870912},
        {"_id": "user789", "total_flux": 268435456}
    ]
    
    tool = CDNQueryTool()
    formatted_results = tool.format_results(mock_results)
    
    print("格式化结果:")
    for result in formatted_results:
        print(result)
    
    tool.close_connection()


if __name__ == "__main__":
    try:
        test_time_conversion()
        test_format_bytes()
        test_query_simulation()
        print("\n✅ 所有测试完成")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
