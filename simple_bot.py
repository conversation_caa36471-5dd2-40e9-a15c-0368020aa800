#!/usr/bin/env python3
"""
简化版Telegram机器人 - 用于测试基本功能
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta, date

from telegram import Update, BotCommand
from telegram.ext import Application, CommandHandler, ContextTypes
from telegram.constants import ParseMode

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)


class SimpleCDNBot:
    """简化版CDN统计查询Telegram机器人"""

    def __init__(self, token: str, allowed_users: list):
        self.token = token
        self.allowed_users = set(allowed_users)
        self.application = Application.builder().token(token).build()
        self.setup_handlers()

    def setup_handlers(self):
        """设置命令处理器"""
        handlers = [
            CommandHandler("start", self.start_command),
            <PERSON><PERSON><PERSON><PERSON>("help", self.help_command),
            <PERSON><PERSON><PERSON><PERSON>("test", self.test_command),
            <PERSON><PERSON><PERSON><PERSON>("ping", self.ping_command),
        ]

        for handler in handlers:
            self.application.add_handler(handler)

        # 错误处理
        self.application.add_error_handler(self.error_handler)

    def check_permission(self, user_id: int) -> bool:
        """检查用户权限"""
        return user_id in self.allowed_users

    async def permission_required(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """权限检查"""
        user_id = update.effective_user.id
        if not self.check_permission(user_id):
            await update.message.reply_text(
                f"❌ 抱歉，您没有权限使用此机器人。\n"
                f"您的用户ID: `{user_id}`\n"
                f"请联系管理员获取权限。",
                parse_mode=ParseMode.MARKDOWN
            )
            return False
        return True

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """开始命令"""
        if not await self.permission_required(update, context):
            return

        user = update.effective_user
        welcome_text = f"""
🚀 **CDN统计查询机器人 (测试版)**

欢迎 {user.first_name}！

这是机器人的测试版本，用于验证基本功能。

**可用命令：**
• `/start` - 显示此消息
• `/help` - 查看帮助
• `/test` - 测试机器人功能
• `/ping` - 检查机器人状态

机器人正在运行中！ ✅
        """

        await update.message.reply_text(welcome_text, parse_mode=ParseMode.MARKDOWN)

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """帮助命令"""
        if not await self.permission_required(update, context):
            return

        help_text = """
📖 **简化版机器人帮助**

**基本命令：**
• `/start` - 开始使用机器人
• `/help` - 显示此帮助信息
• `/test` - 测试机器人功能
• `/ping` - 检查机器人状态

**状态信息：**
• 机器人版本：测试版 v1.0
• 运行状态：正常 ✅
• 权限控制：已启用 🔒

这是一个简化版本，用于测试Telegram机器人的基本功能。
        """

        await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)

    async def test_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """测试命令"""
        if not await self.permission_required(update, context):
            return

        # 发送处理中消息
        processing_msg = await update.message.reply_text("🔄 正在运行测试...")

        try:
            # 模拟一些测试
            await asyncio.sleep(1)  # 模拟处理时间

            test_results = [
                "✅ 机器人响应正常",
                "✅ 权限检查通过",
                "✅ 消息发送功能正常",
                "✅ 异步处理正常"
            ]

            # 检查配置文件
            if os.path.exists('.env'):
                test_results.append("✅ 主配置文件存在")
            else:
                test_results.append("❌ 主配置文件不存在")

            if os.path.exists('.env.bot'):
                test_results.append("✅ 机器人配置文件存在")
            else:
                test_results.append("❌ 机器人配置文件不存在")

            # 检查Python模块
            try:
                import pymongo
                test_results.append("✅ pymongo 模块可用")
            except ImportError:
                test_results.append("❌ pymongo 模块不可用")

            try:
                import pymysql
                test_results.append("✅ pymysql 模块可用")
            except ImportError:
                test_results.append("❌ pymysql 模块不可用")

            result_text = "🔧 **测试结果**\n\n" + "\n".join(test_results)
            result_text += f"\n\n⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            await processing_msg.edit_text(result_text, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            await processing_msg.edit_text(f"❌ 测试失败：{str(e)}")

    async def ping_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Ping命令"""
        if not await self.permission_required(update, context):
            return

        start_time = datetime.now()
        msg = await update.message.reply_text("🏓 Pong!")
        end_time = datetime.now()

        response_time = (end_time - start_time).total_seconds() * 1000

        await msg.edit_text(
            f"🏓 **Pong!**\n\n"
            f"⚡ 响应时间: {response_time:.2f}ms\n"
            f"🕐 服务器时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"👤 用户ID: {update.effective_user.id}",
            parse_mode=ParseMode.MARKDOWN
        )

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE):
        """错误处理器"""
        logger.error(f"Exception while handling an update: {context.error}")

        if isinstance(update, Update) and update.message:
            await update.message.reply_text(
                "❌ 发生了一个错误，请稍后重试或联系管理员。"
            )

    async def setup_commands(self):
        """设置机器人命令菜单"""
        commands = [
            BotCommand("start", "开始使用机器人"),
            BotCommand("help", "查看帮助信息"),
            BotCommand("test", "测试机器人功能"),
            BotCommand("ping", "检查机器人状态"),
        ]

        await self.application.bot.set_my_commands(commands)

    async def run(self):
        """运行机器人"""
        # 设置命令菜单
        await self.setup_commands()

        logger.info("Simple CDN Telegram Bot started successfully!")

        # 启动机器人并保持运行
        async with self.application:
            await self.application.start()
            await self.application.updater.start_polling()

            # 保持运行直到收到停止信号
            await self.application.updater.idle()


def main():
    """主函数"""
    print("🤖 启动简化版CDN Telegram机器人...")

    # 加载环境变量
    try:
        from dotenv import load_dotenv
        if os.path.exists('.env.bot'):
            load_dotenv('.env.bot')
            print("✅ 已加载 .env.bot 配置文件")
        elif os.path.exists('.env'):
            load_dotenv('.env')
            print("✅ 已加载 .env 配置文件")
    except ImportError:
        print("⚠️  python-dotenv 未安装，请手动设置环境变量")

    # 从环境变量获取配置
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not bot_token:
        print("❌ 请设置环境变量 TELEGRAM_BOT_TOKEN")
        sys.exit(1)

    # 从环境变量获取允许的用户ID
    allowed_users_str = os.getenv('TELEGRAM_ALLOWED_USERS', '')
    if not allowed_users_str:
        print("❌ 请设置环境变量 TELEGRAM_ALLOWED_USERS")
        sys.exit(1)

    try:
        allowed_users = [int(uid.strip()) for uid in allowed_users_str.split(',')]
    except ValueError:
        print("❌ TELEGRAM_ALLOWED_USERS 格式错误")
        sys.exit(1)

    print(f"📋 允许的用户数量: {len(allowed_users)}")
    print(f"🔑 Bot Token: {bot_token[:10]}...")

    # 创建并运行机器人
    bot = SimpleCDNBot(bot_token, allowed_users)

    try:
        asyncio.run(bot.run())
    except KeyboardInterrupt:
        print("\n🛑 机器人已停止")
    except Exception as e:
        print(f"❌ 机器人运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
