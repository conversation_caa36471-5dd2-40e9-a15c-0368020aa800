# ===========================================
# SSH隧道模式配置模板
# 适用于通过SSH隧道连接MongoDB的场景
# ===========================================

# MongoDB 配置（通过SSH隧道连接）
MONGODB_URI=mongodb://localhost:27017/
DATABASE_NAME=your_database_name
COLLECTION_NAME=cdn_statistics_total_metric

# SSH 隧道配置（启用）
USE_SSH_TUNNEL=true
SSH_HOST=your_ssh_server_host
SSH_PORT=22
SSH_USERNAME=your_ssh_username

# SSH认证方式1：使用密码
SSH_PASSWORD=your_ssh_password
SSH_KEY_FILE=

# SSH认证方式2：使用密钥文件（推荐，注释掉上面的密码行）
# SSH_PASSWORD=
# SSH_KEY_FILE=/path/to/your/ssh/private/key

# 远程MongoDB配置（SSH服务器上的实际MongoDB）
REMOTE_MONGODB_HOST=*************
REMOTE_MONGODB_PORT=27017
REMOTE_MONGODB_USERNAME=mongouser
REMOTE_MONGODB_PASSWORD=your_mongodb_password
REMOTE_MONGODB_AUTH_SOURCE=admin

# 本地隧道端口
LOCAL_TUNNEL_PORT=27018

# MySQL 配置（用于查询用户邮箱）
ENABLE_USER_EMAIL_LOOKUP=true
MYSQL_HOST=your_mysql_host
MYSQL_PORT=3306
MYSQL_USERNAME=your_mysql_username
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=your_mysql_database
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai

# ===========================================
# 配置说明：
# 1. 设置SSH服务器连接信息
# 2. 选择SSH认证方式（密码或密钥）
# 3. 设置MongoDB认证信息（mongouser的密码）
# 4. 配置MySQL连接信息（如需查询用户邮箱）
# 5. 保存后重命名为 .env 文件
# 
# 工作原理：
# 工具会自动创建SSH隧道：
# 本地端口27018 -> SSH服务器 -> *************:27017
# 然后通过本地端口连接MongoDB
# ===========================================
