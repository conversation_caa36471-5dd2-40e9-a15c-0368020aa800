#!/usr/bin/env python3
"""
简化的打包脚本，专门处理PyInstaller问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(cmd, description=""):
    """运行命令并处理结果"""
    print(f"🔧 {description}")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ {description} 超时")
        return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False


def check_pyinstaller():
    """检查PyInstaller是否可用"""
    print("🔍 检查PyInstaller...")
    
    # 方法1: 检查命令
    if run_command(['pyinstaller', '--version'], "检查pyinstaller命令"):
        return True
    
    # 方法2: 通过Python模块运行
    if run_command([sys.executable, '-m', 'PyInstaller', '--version'], "通过Python模块检查"):
        return True
    
    # 方法3: 尝试导入
    try:
        import PyInstaller
        print(f"✅ PyInstaller模块导入成功，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller模块导入失败")
    
    return False


def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 安装PyInstaller...")
    
    commands = [
        ([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], "升级pip"),
        ([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], "安装PyInstaller"),
    ]
    
    for cmd, desc in commands:
        if not run_command(cmd, desc):
            return False
    
    return True


def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除目录: {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file_path in glob.glob(pattern):
            os.remove(file_path)
            print(f"   删除文件: {file_path}")


def build_single_executable():
    """构建单个可执行文件"""
    print("🔨 构建单个可执行文件...")
    
    # 基本的PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--name', 'cdn-query',
        '--distpath', 'dist',
        '--workpath', 'build',
        '--add-data', '.env.example:.',
        '--add-data', 'README.md:.',
        '--hidden-import', 'pymongo',
        '--hidden-import', 'pymysql',
        '--hidden-import', 'paramiko',
        '--hidden-import', 'sshtunnel',
        '--hidden-import', 'click',
        '--hidden-import', 'tabulate',
        '--hidden-import', 'dotenv',
        '--hidden-import', 'dateutil',
        'main.py'
    ]
    
    # 如果pyinstaller命令不可用，尝试通过Python模块运行
    if not run_command(['pyinstaller', '--version'], "检查pyinstaller命令"):
        print("尝试通过Python模块运行PyInstaller...")
        cmd[0] = sys.executable
        cmd.insert(1, '-m')
        cmd.insert(2, 'PyInstaller')
    
    return run_command(cmd, "构建可执行文件")


def build_directory():
    """构建目录形式的分发包"""
    print("📁 构建目录分发包...")
    
    cmd = [
        'pyinstaller',
        '--onedir',
        '--name', 'cdn-stats-tool',
        '--distpath', 'dist',
        '--workpath', 'build',
        '--add-data', '.env.example:.',
        '--add-data', 'README.md:.',
        '--hidden-import', 'pymongo',
        '--hidden-import', 'pymysql',
        '--hidden-import', 'paramiko',
        '--hidden-import', 'sshtunnel',
        '--hidden-import', 'click',
        '--hidden-import', 'tabulate',
        '--hidden-import', 'dotenv',
        '--hidden-import', 'dateutil',
        'main.py'
    ]
    
    # 如果pyinstaller命令不可用，尝试通过Python模块运行
    if not run_command(['pyinstaller', '--version'], "检查pyinstaller命令"):
        cmd[0] = sys.executable
        cmd.insert(1, '-m')
        cmd.insert(2, 'PyInstaller')
    
    return run_command(cmd, "构建目录分发包")


def create_portable_package():
    """创建便携版包"""
    print("💼 创建便携版包...")
    
    portable_dir = "cdn-stats-tool-portable"
    
    try:
        # 创建目录
        if os.path.exists(portable_dir):
            shutil.rmtree(portable_dir)
        os.makedirs(portable_dir)
        
        # 复制文件
        files_to_copy = [
            'main.py', 'cdn_query_tool.py', 'mysql_manager.py',
            'ssh_tunnel_manager.py', 'config.py', 'batch_export.py',
            'setup_wizard.py', 'configure_ssh_tunnel.py',
            'requirements.txt', '.env.example', '.env.ssh_template'
        ]
        
        for file in files_to_copy:
            if os.path.exists(file):
                shutil.copy2(file, portable_dir)
                print(f"   复制: {file}")
        
        # 复制文档
        doc_files = ['README.md', 'USER_MANUAL.md', 'BATCH_EXPORT_GUIDE.md']
        for file in doc_files:
            if os.path.exists(file):
                shutil.copy2(file, portable_dir)
        
        # 创建启动脚本
        create_start_scripts(portable_dir)
        
        print(f"✅ 便携版包创建成功: {portable_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 便携版包创建失败: {e}")
        return False


def create_start_scripts(portable_dir):
    """创建启动脚本"""
    
    # Windows启动脚本
    bat_content = '''@echo off
echo CDN统计查询工具
echo ================
echo.

python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python
    echo 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 安装依赖包...
pip install -r requirements.txt

echo.
echo 启动工具...
python main.py %*

if "%1"=="" pause
'''
    
    with open(os.path.join(portable_dir, 'start.bat'), 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    # Linux/macOS启动脚本
    sh_content = '''#!/bin/bash

echo "CDN统计查询工具"
echo "================"
echo ""

if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3"
    echo "请先安装Python 3.8或更高版本"
    exit 1
fi

echo "安装依赖包..."
pip3 install -r requirements.txt

echo ""
echo "启动工具..."
python3 main.py "$@"
'''
    
    sh_file = os.path.join(portable_dir, 'start.sh')
    with open(sh_file, 'w', encoding='utf-8') as f:
        f.write(sh_content)
    os.chmod(sh_file, 0o755)


def main():
    """主函数"""
    print("🚀 CDN统计查询工具 - 简化打包程序")
    print("=" * 40)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        print("\n❌ PyInstaller不可用")
        
        response = input("是否尝试安装PyInstaller? (y/n): ").lower()
        if response in ['y', 'yes']:
            if not install_pyinstaller():
                print("❌ PyInstaller安装失败")
                return
            
            # 重新检查
            if not check_pyinstaller():
                print("❌ PyInstaller安装后仍不可用")
                return
        else:
            print("跳过PyInstaller打包，只创建便携版...")
            create_portable_package()
            return
    
    print("\n请选择打包方式:")
    print("1. 单个可执行文件 (推荐)")
    print("2. 目录形式分发包")
    print("3. 便携版包 (需要Python环境)")
    print("4. 全部打包")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
    except KeyboardInterrupt:
        print("\n用户取消")
        return
    
    # 清理构建目录
    clean_build()
    
    success_count = 0
    
    if choice == '1':
        if build_single_executable():
            success_count += 1
    elif choice == '2':
        if build_directory():
            success_count += 1
    elif choice == '3':
        if create_portable_package():
            success_count += 1
    elif choice == '4':
        if build_single_executable():
            success_count += 1
        if build_directory():
            success_count += 1
        if create_portable_package():
            success_count += 1
    else:
        print("无效选择")
        return
    
    print(f"\n📊 打包完成，成功: {success_count}")
    
    if success_count > 0:
        print("\n📁 生成的文件:")
        if os.path.exists('dist'):
            print("dist/ 目录:")
            for item in os.listdir('dist'):
                print(f"  - {item}")
        
        if os.path.exists('cdn-stats-tool-portable'):
            print("便携版: cdn-stats-tool-portable/")
    
    print("\n🎉 打包完成！")


if __name__ == "__main__":
    main()
