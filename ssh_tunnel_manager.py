import os
import time
from typing import Optional
from sshtunnel import SSHTunnelForwarder
from config import Config


class SSHTunnelManager:
    """SSH隧道管理器"""

    def __init__(self):
        self.tunnel: Optional[SSHTunnelForwarder] = None
        self.config = Config.get_ssh_config()

    def create_tunnel(self) -> bool:
        """创建SSH隧道"""
        if not self.config['use_ssh_tunnel']:
            print("未启用SSH隧道")
            return False

        try:
            print(f"正在创建SSH隧道到 {self.config['ssh_host']}:{self.config['ssh_port']}")

            # 准备SSH认证参数
            ssh_kwargs = {
                'ssh_host': self.config['ssh_host'],
                'ssh_port': self.config['ssh_port'],
                'ssh_username': self.config['ssh_username'],
                'remote_bind_address': (
                    self.config['remote_mongodb_host'],
                    self.config['remote_mongodb_port']
                ),
                'local_bind_address': ('127.0.0.1', self.config['local_tunnel_port'])
            }

            # 选择认证方式
            if self.config['ssh_key_file'] and os.path.exists(self.config['ssh_key_file']):
                print(f"使用SSH密钥文件: {self.config['ssh_key_file']}")
                ssh_kwargs['ssh_pkey'] = self.config['ssh_key_file']
            elif self.config['ssh_password']:
                print("使用SSH密码认证")
                ssh_kwargs['ssh_password'] = self.config['ssh_password']
            else:
                print("错误: 未提供SSH密钥文件或密码")
                return False

            # 创建隧道
            self.tunnel = SSHTunnelForwarder(**ssh_kwargs)

            # 启动隧道
            self.tunnel.start()

            # 等待隧道建立
            time.sleep(2)

            if self.tunnel.is_alive:
                local_port = self.tunnel.local_bind_port
                print(f"SSH隧道创建成功! 本地端口: {local_port}")
                print(f"MongoDB连接地址: mongodb://localhost:{local_port}/")
                return True
            else:
                print("SSH隧道创建失败")
                return False

        except Exception as e:
            print(f"创建SSH隧道时出错: {e}")
            return False

    def close_tunnel(self):
        """关闭SSH隧道"""
        if self.tunnel and self.tunnel.is_alive:
            print("正在关闭SSH隧道...")
            self.tunnel.stop()
            self.tunnel = None
            print("SSH隧道已关闭")

    def get_local_mongodb_uri(self) -> str:
        """获取通过隧道的本地MongoDB连接URI"""
        if self.tunnel and self.tunnel.is_alive:
            local_port = self.tunnel.local_bind_port

            # 构建包含认证信息的MongoDB URI
            username = self.config.get('remote_mongodb_username', '')
            password = self.config.get('remote_mongodb_password', '')
            auth_source = self.config.get('remote_mongodb_auth_source', 'admin')

            if username and password:
                # URL编码用户名和密码
                from urllib.parse import quote_plus
                encoded_username = quote_plus(username)
                encoded_password = quote_plus(password)
                return f"mongodb://{encoded_username}:{encoded_password}@localhost:{local_port}/?authSource={auth_source}"
            else:
                return f"mongodb://localhost:{local_port}/"
        else:
            return Config.MONGODB_URI

    def is_tunnel_active(self) -> bool:
        """检查隧道是否活跃"""
        return self.tunnel is not None and self.tunnel.is_alive

    def __enter__(self):
        """上下文管理器入口"""
        if self.config['use_ssh_tunnel']:
            if self.create_tunnel():
                return self
            else:
                raise Exception("无法创建SSH隧道")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_tunnel()
