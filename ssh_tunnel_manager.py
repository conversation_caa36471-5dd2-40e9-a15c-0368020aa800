import os
import time
from typing import Optional
from sshtunnel import SSHTunnelForwarder
from config import Config


class SSHTunnelManager:
    """SSH隧道管理器，支持MongoDB和MySQL双隧道"""

    def __init__(self):
        self.mongodb_tunnel: Optional[SSHTunnelForwarder] = None
        self.mysql_tunnel: Optional[SSHTunnelForwarder] = None
        self.config = Config.get_ssh_config()

    def create_tunnel(self) -> bool:
        """创建SSH隧道（MongoDB和MySQL）"""
        if not self.config['use_ssh_tunnel']:
            print("未启用SSH隧道")
            return False

        success = True

        # 创建MongoDB隧道
        if not self._create_mongodb_tunnel():
            success = False

        # 创建MySQL隧道（如果启用了MySQL查询）
        mysql_config = Config.get_mysql_config()
        if mysql_config['enable_lookup']:
            if not self._create_mysql_tunnel():
                success = False

        return success

    def _create_mongodb_tunnel(self) -> bool:
        """创建MongoDB SSH隧道"""
        try:
            print(f"正在创建MongoDB SSH隧道到 {self.config['ssh_host']}:{self.config['ssh_port']}")

            # 准备SSH认证参数
            ssh_kwargs = self._get_ssh_auth_params()
            ssh_kwargs.update({
                'remote_bind_address': (
                    self.config['remote_mongodb_host'],
                    self.config['remote_mongodb_port']
                ),
                'local_bind_address': ('127.0.0.1', self.config['local_tunnel_port'])
            })

            # 创建MongoDB隧道
            self.mongodb_tunnel = SSHTunnelForwarder(**ssh_kwargs)
            self.mongodb_tunnel.start()
            time.sleep(2)

            if self.mongodb_tunnel.is_alive:
                local_port = self.mongodb_tunnel.local_bind_port
                print(f"MongoDB SSH隧道创建成功! 本地端口: {local_port}")
                return True
            else:
                print("MongoDB SSH隧道创建失败")
                return False

        except Exception as e:
            print(f"创建MongoDB SSH隧道时出错: {e}")
            return False

    def _create_mysql_tunnel(self) -> bool:
        """创建MySQL SSH隧道"""
        try:
            print(f"正在创建MySQL SSH隧道到 {self.config['ssh_host']}:{self.config['ssh_port']}")

            # 准备SSH认证参数
            ssh_kwargs = self._get_ssh_auth_params()
            ssh_kwargs.update({
                'remote_bind_address': (
                    self.config['remote_mysql_host'],
                    self.config['remote_mysql_port']
                ),
                'local_bind_address': ('127.0.0.1', self.config['local_mysql_tunnel_port'])
            })

            # 创建MySQL隧道
            self.mysql_tunnel = SSHTunnelForwarder(**ssh_kwargs)
            self.mysql_tunnel.start()
            time.sleep(2)

            if self.mysql_tunnel.is_alive:
                local_port = self.mysql_tunnel.local_bind_port
                print(f"MySQL SSH隧道创建成功! 本地端口: {local_port}")
                return True
            else:
                print("MySQL SSH隧道创建失败")
                return False

        except Exception as e:
            print(f"创建MySQL SSH隧道时出错: {e}")
            return False

    def _get_ssh_auth_params(self) -> dict:
        """获取SSH认证参数"""
        ssh_kwargs = {
            'ssh_host': self.config['ssh_host'],
            'ssh_port': self.config['ssh_port'],
            'ssh_username': self.config['ssh_username']
        }

        # 选择认证方式
        if self.config['ssh_key_file'] and os.path.exists(self.config['ssh_key_file']):
            print(f"使用SSH密钥文件: {self.config['ssh_key_file']}")
            ssh_kwargs['ssh_pkey'] = self.config['ssh_key_file']
        elif self.config['ssh_password']:
            print("使用SSH密码认证")
            ssh_kwargs['ssh_password'] = self.config['ssh_password']
        else:
            raise Exception("未提供SSH密钥文件或密码")

        return ssh_kwargs

    def close_tunnel(self):
        """关闭SSH隧道"""
        if self.mongodb_tunnel and self.mongodb_tunnel.is_alive:
            print("正在关闭MongoDB SSH隧道...")
            self.mongodb_tunnel.stop()
            self.mongodb_tunnel = None
            print("MongoDB SSH隧道已关闭")

        if self.mysql_tunnel and self.mysql_tunnel.is_alive:
            print("正在关闭MySQL SSH隧道...")
            self.mysql_tunnel.stop()
            self.mysql_tunnel = None
            print("MySQL SSH隧道已关闭")

    def get_local_mongodb_uri(self) -> str:
        """获取通过隧道的本地MongoDB连接URI"""
        if self.mongodb_tunnel and self.mongodb_tunnel.is_alive:
            local_port = self.mongodb_tunnel.local_bind_port

            # 构建包含认证信息的MongoDB URI
            username = self.config.get('remote_mongodb_username', '')
            password = self.config.get('remote_mongodb_password', '')
            auth_source = self.config.get('remote_mongodb_auth_source', 'admin')

            if username and password:
                # URL编码用户名和密码
                from urllib.parse import quote_plus
                encoded_username = quote_plus(username)
                encoded_password = quote_plus(password)
                return f"mongodb://{encoded_username}:{encoded_password}@localhost:{local_port}/?authSource={auth_source}"
            else:
                return f"mongodb://localhost:{local_port}/"
        else:
            return Config.MONGODB_URI

    def get_local_mysql_config(self) -> dict:
        """获取通过隧道的本地MySQL连接配置"""
        if self.mysql_tunnel and self.mysql_tunnel.is_alive:
            local_port = self.mysql_tunnel.local_bind_port
            return {
                'host': 'localhost',
                'port': local_port
            }
        else:
            mysql_config = Config.get_mysql_config()
            return {
                'host': mysql_config['host'],
                'port': mysql_config['port']
            }

    def is_tunnel_active(self) -> bool:
        """检查隧道是否活跃"""
        mongodb_active = self.mongodb_tunnel is not None and self.mongodb_tunnel.is_alive
        mysql_active = True  # MySQL隧道是可选的

        mysql_config = Config.get_mysql_config()
        if mysql_config['enable_lookup']:
            mysql_active = self.mysql_tunnel is not None and self.mysql_tunnel.is_alive

        return mongodb_active and mysql_active

    def __enter__(self):
        """上下文管理器入口"""
        if self.config['use_ssh_tunnel']:
            if self.create_tunnel():
                return self
            else:
                raise Exception("无法创建SSH隧道")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_tunnel()
