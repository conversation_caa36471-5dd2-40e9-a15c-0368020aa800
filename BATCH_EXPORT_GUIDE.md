# CDN统计数据批量导出指南

## 概述

提供了三种批量导出方式，用于统计从2025年5月20日到昨天的CDN数据，并按日期生成CSV文件。

## 方式1: Python脚本（推荐）

### 快速使用
```bash
# 直接运行，使用默认设置（从2025-05-20到昨天）
python batch_export.py
```

### 自定义参数
```bash
# 指定日期范围
python batch_export.py -s 2025-05-20 -e 2025-05-30

# 指定输出目录
python batch_export.py -o my_exports

# 设置查询间隔（避免数据库压力）
python batch_export.py -d 2

# 完整示例
python batch_export.py -s 2025-05-20 -e 2025-05-30 -o exports_may -d 1
```

### 参数说明
- `-s, --start-date`: 开始日期（默认：2025-05-20）
- `-e, --end-date`: 结束日期（默认：昨天）
- `-o, --output-dir`: 输出目录（默认：自动生成）
- `-d, --delay`: 查询间隔秒数（默认：1秒）

## 方式2: Shell脚本（简化版）

```bash
# 给脚本执行权限
chmod +x export_daily_stats.sh

# 运行脚本
./export_daily_stats.sh
```

## 方式3: Shell脚本（完整版）

```bash
# 给脚本执行权限
chmod +x batch_export_csv.sh

# 使用默认设置
./batch_export_csv.sh

# 自定义参数
./batch_export_csv.sh -s 2025-05-20 -e 2025-05-30 -o my_exports
```

## 输出结果

### 文件结构
```
csv_exports_20241220_143022/
├── cdn_stats_2025-05-20.csv
├── cdn_stats_2025-05-21.csv
├── cdn_stats_2025-05-22.csv
├── ...
├── cdn_stats_2024-12-19.csv
└── export_summary.txt
```

### CSV文件格式
每个CSV文件包含以下列：
- 排名
- 用户ID
- 总流量(字节)
- 用户邮箱
- 总流量(可读)

### 汇总报告示例
```
CDN统计数据导出汇总报告
==============================
导出时间: 2024-12-20 14:30:22
日期范围: 2025-05-20 到 2024-12-19
总天数: 213
成功导出: 210 天
失败/无数据: 3 天
总记录数: 125,430

文件列表:
--------------------
cdn_stats_2025-05-20.csv: 1,250 条记录
cdn_stats_2025-05-21.csv: 1,180 条记录
...
```

## 使用建议

### 1. 性能优化
- 使用 `-d` 参数设置查询间隔，避免对数据库造成压力
- 大量数据导出时建议在非高峰期进行

### 2. 错误处理
- 如果某天导出失败，脚本会继续处理其他日期
- 检查汇总报告了解失败的具体日期

### 3. 数据验证
```bash
# 检查某个文件的记录数
wc -l csv_exports_*/cdn_stats_2025-05-20.csv

# 查看文件内容
head -5 csv_exports_*/cdn_stats_2025-05-20.csv

# 统计总记录数
tail -n +2 csv_exports_*/*.csv | wc -l
```

### 4. 数据合并
```bash
# 合并所有CSV文件（保留标题）
head -1 csv_exports_*/cdn_stats_2025-05-20.csv > all_data.csv
tail -n +2 csv_exports_*/*.csv >> all_data.csv

# 或者使用Python合并
python -c "
import pandas as pd
import glob
files = glob.glob('csv_exports_*/*.csv')
df = pd.concat([pd.read_csv(f) for f in files])
df.to_csv('merged_data.csv', index=False)
"
```

## 故障排除

### 1. 数据库连接问题
```bash
# 测试数据库连接
python main.py test-connection
python main.py test-mysql
```

### 2. SSH隧道问题
```bash
# 测试SSH隧道
python main.py test-ssh
```

### 3. 权限问题
```bash
# 给脚本执行权限
chmod +x *.sh

# 检查输出目录权限
ls -la csv_exports_*/
```

### 4. 内存不足
- 如果数据量很大，可以分批导出
- 使用 `--limit` 参数限制每天的记录数

## 监控进度

### 实时监控
```bash
# 监控输出目录
watch -n 5 "ls -la csv_exports_*/ | tail -10"

# 监控文件大小
watch -n 5 "du -sh csv_exports_*/"
```

### 日志记录
```bash
# 将输出保存到日志文件
python batch_export.py 2>&1 | tee export.log
```

## 自动化运行

### 定时任务
```bash
# 编辑crontab
crontab -e

# 每天凌晨2点运行（导出前一天的数据）
0 2 * * * cd /path/to/project && python batch_export.py -s $(date -d "yesterday" +\%Y-\%m-\%d) -e $(date -d "yesterday" +\%Y-\%m-\%d)
```

### 脚本化
```bash
#!/bin/bash
# auto_export.sh
cd /path/to/your/project
python batch_export.py -s 2025-05-20 -o /backup/cdn_exports
```

## 注意事项

1. **数据库负载**: 大量查询可能对数据库造成压力，建议设置适当的延迟
2. **磁盘空间**: 确保有足够的磁盘空间存储CSV文件
3. **网络稳定**: SSH隧道需要稳定的网络连接
4. **时区问题**: 确认查询的时间范围符合预期
5. **数据一致性**: 建议在数据更新较少的时间段进行导出
