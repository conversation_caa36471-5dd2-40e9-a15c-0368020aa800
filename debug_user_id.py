#!/usr/bin/env python3
"""
用户ID格式调试脚本
检查MongoDB中的用户ID格式和MySQL查询问题
"""

from cdn_query_tool import CDNQueryTool
from mysql_manager import MySQLManager
from config import Config
from datetime import datetime, timedelta


def test_mongodb_user_ids():
    """测试MongoDB中的用户ID格式"""
    print("=== MongoDB用户ID格式测试 ===")
    
    try:
        tool = CDNQueryTool()
        
        # 查询少量数据进行测试
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        print(f"查询日期: {yesterday}")
        
        results = tool.query_flux_by_date_range(
            start_date=yesterday,
            limit=5  # 只查询5条记录用于测试
        )
        
        if results:
            print(f"查询到 {len(results)} 条记录")
            print("\n用户ID格式分析:")
            
            for i, result in enumerate(results, 1):
                user_id = result['_id']
                user_id_str = str(user_id)
                user_id_type = type(user_id).__name__
                
                print(f"记录 {i}:")
                print(f"  原始值: {user_id}")
                print(f"  类型: {user_id_type}")
                print(f"  字符串: {user_id_str}")
                print(f"  长度: {len(user_id_str)}")
                
                # 检查是否包含科学计数法
                if 'e' in user_id_str.lower():
                    print(f"  ⚠️  包含科学计数法!")
                    # 尝试转换为整数再转字符串
                    try:
                        if isinstance(user_id, (int, float)):
                            fixed_id = str(int(user_id))
                            print(f"  修正后: {fixed_id}")
                        else:
                            fixed_id = str(int(float(user_id_str)))
                            print(f"  修正后: {fixed_id}")
                    except:
                        print(f"  无法修正")
                print()
        else:
            print("没有查询到数据")
            
        tool.close_connection()
        return results
        
    except Exception as e:
        print(f"MongoDB测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []


def test_mysql_query_with_ids(user_ids):
    """使用具体的用户ID测试MySQL查询"""
    print("=== MySQL查询测试 ===")
    
    config = Config.get_mysql_config()
    if not config['enable_lookup']:
        print("MySQL查询功能未启用")
        return
    
    try:
        # 创建SSH隧道（如果需要）
        tunnel_manager = None
        if config['use_ssh_tunnel']:
            from ssh_tunnel_manager import SSHTunnelManager
            tunnel_manager = SSHTunnelManager()
            if not tunnel_manager.create_tunnel():
                print("SSH隧道创建失败")
                return
        
        mysql_manager = MySQLManager(tunnel_manager=tunnel_manager)
        
        print(f"测试用户ID: {user_ids}")
        
        # 测试单个用户查询
        for user_id in user_ids:
            print(f"\n--- 测试用户ID: {user_id} ---")
            
            # 确保转换为字符串
            user_id_str = str(user_id)
            if 'e' in user_id_str.lower():
                # 处理科学计数法
                try:
                    if isinstance(user_id, (int, float)):
                        user_id_str = str(int(user_id))
                    else:
                        user_id_str = str(int(float(user_id_str)))
                    print(f"修正科学计数法: {user_id} -> {user_id_str}")
                except:
                    print(f"无法修正科学计数法: {user_id}")
            
            email = mysql_manager.get_user_email(user_id_str)
            if email:
                print(f"✅ 找到邮箱: {email}")
            else:
                print(f"❌ 未找到邮箱")
        
        # 测试批量查询
        print(f"\n--- 批量查询测试 ---")
        user_ids_str = [str(uid) for uid in user_ids]
        # 处理科学计数法
        fixed_user_ids = []
        for uid in user_ids_str:
            if 'e' in uid.lower():
                try:
                    fixed_uid = str(int(float(uid)))
                    fixed_user_ids.append(fixed_uid)
                    print(f"修正: {uid} -> {fixed_uid}")
                except:
                    fixed_user_ids.append(uid)
            else:
                fixed_user_ids.append(uid)
        
        user_emails = mysql_manager.batch_get_user_emails(fixed_user_ids)
        print(f"批量查询结果: {user_emails}")
        
        mysql_manager.close_connection()
        if tunnel_manager:
            tunnel_manager.close_tunnel()
            
    except Exception as e:
        print(f"MySQL测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_mysql_direct_query():
    """直接测试MySQL查询"""
    print("\n=== MySQL直接查询测试 ===")
    
    config = Config.get_mysql_config()
    if not config['enable_lookup']:
        print("MySQL查询功能未启用")
        return
    
    try:
        # 创建SSH隧道（如果需要）
        tunnel_manager = None
        if config['use_ssh_tunnel']:
            from ssh_tunnel_manager import SSHTunnelManager
            tunnel_manager = SSHTunnelManager()
            if not tunnel_manager.create_tunnel():
                print("SSH隧道创建失败")
                return
        
        mysql_manager = MySQLManager(tunnel_manager=tunnel_manager)
        
        # 查询前几个用户作为示例
        with mysql_manager.connection.cursor() as cursor:
            sql = f"""
            SELECT {config['user_id_field']}, {config['user_email_field']} 
            FROM {config['user_table']} 
            LIMIT 5
            """
            print(f"执行查询: {sql}")
            cursor.execute(sql)
            results = cursor.fetchall()
            
            print(f"查询结果 ({len(results)} 条):")
            for row in results:
                user_id = row[config['user_id_field']]
                email = row[config['user_email_field']]
                print(f"  用户ID: {user_id} (类型: {type(user_id).__name__})")
                print(f"  邮箱: {email}")
                print()
        
        mysql_manager.close_connection()
        if tunnel_manager:
            tunnel_manager.close_tunnel()
            
    except Exception as e:
        print(f"MySQL直接查询失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("用户ID格式调试工具")
    print("=" * 50)
    
    # 1. 测试MongoDB用户ID格式
    mongodb_results = test_mongodb_user_ids()
    
    # 2. 测试MySQL直接查询
    test_mysql_direct_query()
    
    # 3. 如果有MongoDB结果，测试MySQL查询
    if mongodb_results:
        user_ids = [result['_id'] for result in mongodb_results]
        test_mysql_query_with_ids(user_ids)
    
    print("\n" + "=" * 50)
    print("调试完成")
    print("\n建议:")
    print("1. 检查用户ID是否使用了科学计数法")
    print("2. 确认MySQL中的用户ID字段类型")
    print("3. 验证SSH隧道连接是否正常")


if __name__ == "__main__":
    main()
