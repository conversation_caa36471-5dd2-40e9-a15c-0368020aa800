# 项目文件说明

## 核心文件

### 主程序
- `main.py` - 主程序入口，命令行界面
- `cdn_query_tool.py` - 核心查询逻辑
- `config.py` - 配置管理

### 数据库管理
- `mysql_manager.py` - MySQL数据库管理
- `ssh_tunnel_manager.py` - SSH隧道管理

### 批量处理
- `batch_export.py` - 批量导出工具

### Telegram机器人
- `telegram_bot.py` - Telegram机器人主程序
- `bot_config.py` - 机器人配置管理
- `start_bot.py` - 机器人启动脚本

### 配置工具
- `setup_wizard.py` - 数据库配置向导
- `setup_bot.py` - 机器人配置向导

## 配置文件

- `.env` - 主配置文件（数据库连接等）
- `.env.bot` - 机器人配置文件（Bot Token等）
- `.env.example` - 配置文件模板

## 依赖文件

- `requirements.txt` - Python依赖包列表

## 文档文件

- `README.md` - 项目说明文档
- `PROJECT_FILES.md` - 本文件，项目文件说明

## 使用流程

### 1. 命令行工具
```
setup_wizard.py → main.py → cdn_query_tool.py
                ↓
            config.py → ssh_tunnel_manager.py
                     → mysql_manager.py
```

### 2. 批量导出
```
batch_export.py → cdn_query_tool.py → 生成CSV文件
```

### 3. Telegram机器人
```
setup_bot.py → start_bot.py → telegram_bot.py
                            ↓
                        bot_config.py
                            ↓
                        cdn_query_tool.py
```

## 文件依赖关系

- `main.py` 依赖 `cdn_query_tool.py`, `config.py`
- `cdn_query_tool.py` 依赖 `mysql_manager.py`, `ssh_tunnel_manager.py`, `config.py`
- `telegram_bot.py` 依赖 `cdn_query_tool.py`, `bot_config.py`
- 所有模块都依赖 `config.py`

## 配置优先级

1. 环境变量
2. `.env` 文件
3. `.env.bot` 文件（仅机器人）
4. 默认值
