# CDN统计查询工具 - Telegram机器人使用指南

## 概述

Telegram机器人版本将CDN统计查询工具的所有功能集成到Telegram中，支持：
- 📊 数据查询和导出
- 📁 批量CSV文件生成
- 🔒 用户权限控制
- 🚦 请求限流保护
- 📱 移动端友好操作

## 快速开始

### 1. 创建Telegram机器人

1. **联系BotFather**：
   - 在Telegram中搜索 `@BotFather`
   - 发送 `/newbot` 命令

2. **设置机器人**：
   - 输入机器人名称（如：CDN统计查询助手）
   - 输入机器人用户名（如：cdn_stats_bot）
   - 获取Bot Token（保密！）

3. **获取用户ID**：
   - 发送任意消息给 `@userinfobot`
   - 记录您的用户ID

### 2. 配置机器人

#### 方式1: 使用配置向导（推荐）
```bash
# 安装机器人依赖
pip install -r requirements_bot.txt

# 运行配置向导
python setup_bot.py setup
```

#### 方式2: 手动配置
创建 `.env.bot` 文件：
```env
# Bot Token
TELEGRAM_BOT_TOKEN=your_bot_token_here

# 允许使用的用户ID (逗号分隔)
TELEGRAM_ALLOWED_USERS=123456789,987654321

# 管理员用户ID (可选)
TELEGRAM_ADMIN_USERS=123456789

# 功能限制
BOT_MAX_QUERY_DAYS=30
BOT_MAX_BATCH_DAYS=30
BOT_MAX_RECORDS_PER_QUERY=1000
BOT_MAX_FILE_SIZE_MB=50
BOT_RATE_LIMIT_PER_USER=10
```

### 3. 启动机器人

```bash
# 启动机器人
python start_bot.py

# 或使用后台运行
nohup python start_bot.py > bot.log 2>&1 &
```

### 4. 测试机器人

1. 在Telegram中搜索您的机器人
2. 发送 `/start` 开始使用
3. 发送 `/help` 查看命令帮助

## 机器人命令

### 基本命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `/start` | 开始使用机器人 | `/start` |
| `/help` | 查看帮助信息 | `/help` |
| `/status` | 查看系统状态 | `/status` |

### 查询命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `/query <日期>` | 查询指定日期数据 | `/query 2025-05-20` |
| `/query <开始日期> <结束日期>` | 查询日期范围数据 | `/query 2025-05-20 2025-05-25` |

### 批量导出命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `/batch` | 从5月20日到昨天 | `/batch` |
| `/batch <开始日期> <结束日期>` | 指定日期范围 | `/batch 2025-05-20 2025-05-30` |

### 测试命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `/test` | 测试所有连接 | `/test` |
| `/test ssh` | 测试SSH隧道 | `/test ssh` |
| `/test mongo` | 测试MongoDB | `/test mongo` |
| `/test mysql` | 测试MySQL | `/test mysql` |

## 使用示例

### 查询单天数据
```
用户: /query 2025-05-20
机器人: 🔄 正在查询数据，请稍候...
机器人: ✅ 查询完成
       📅 查询日期：2025-05-20
       📊 记录数量：1,250
       📁 文件名：cdn_stats_2025-05-20.csv
       
       前5条记录预览：
       1. 用户ID: 2796484965376 | 邮箱: <EMAIL> | 流量: 1.2 GB
       ...
       
机器人: [发送CSV文件]
```

### 批量导出数据
```
用户: /batch 2025-05-20 2025-05-25
机器人: 🔄 正在批量导出数据...
       📅 日期范围：2025-05-20 到 2025-05-25
       ⏳ 这可能需要几分钟时间，请耐心等待...
       
机器人: 🔄 正在导出数据... 50.0%
       📅 当前处理：2025-05-22
       ✅ 已完成：3 天
       📊 总记录数：3,750
       
机器人: ✅ 批量导出完成
       📅 日期范围：2025-05-20 到 2025-05-25
       📊 总天数：6
       ✅ 成功导出：6 天
       📊 总记录数：7,500
       📁 文件格式：ZIP压缩包
       
机器人: [发送ZIP文件]
```

## 权限控制

### 用户权限
- 只有在 `TELEGRAM_ALLOWED_USERS` 中的用户ID才能使用机器人
- 未授权用户会收到权限拒绝消息
- 显示用户ID以便管理员添加权限

### 管理员权限
- 管理员用户可以执行所有操作
- 未来可扩展管理员专用功能

### 权限检查示例
```
未授权用户: /start
机器人: ❌ 抱歉，您没有权限使用此机器人。
       您的用户ID: 999999999
       请联系管理员获取权限。
```

## 限流保护

### 请求限制
- 每用户每分钟最多10个请求（可配置）
- 超过限制会收到提示消息
- 自动清理过期的请求记录

### 数据限制
- 最大查询天数：30天（可配置）
- 最大批量导出天数：30天（可配置）
- 单次查询最大记录数：1000条（可配置）
- 最大文件大小：50MB（可配置）

## 文件处理

### CSV文件
- 单天查询直接发送CSV文件
- 包含用户ID、邮箱、流量等信息
- 文件名格式：`cdn_stats_2025-05-20.csv`

### ZIP压缩包
- 批量导出生成ZIP压缩包
- 包含多个CSV文件
- 文件名格式：`cdn_batch_export_2025-05-20_to_2025-05-25.zip`

### 文件大小限制
- Telegram文件大小限制：50MB
- 超过限制会提示分批导出
- 自动清理临时文件

## 部署和运维

### 本地运行
```bash
# 前台运行（调试用）
python start_bot.py

# 后台运行
nohup python start_bot.py > bot.log 2>&1 &

# 查看日志
tail -f bot.log
```

### Docker部署
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install -r requirements_bot.txt

CMD ["python", "start_bot.py"]
```

```bash
# 构建镜像
docker build -t cdn-telegram-bot .

# 运行容器
docker run -d \
  --name cdn-bot \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/.env.bot:/app/.env.bot \
  cdn-telegram-bot
```

### 系统服务
创建 `/etc/systemd/system/cdn-bot.service`：
```ini
[Unit]
Description=CDN Statistics Telegram Bot
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/cdn-tool
ExecStart=/usr/bin/python3 start_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 启用服务
sudo systemctl enable cdn-bot
sudo systemctl start cdn-bot

# 查看状态
sudo systemctl status cdn-bot
```

## 监控和日志

### 日志文件
- 机器人日志：`bot.log`
- 包含用户操作、错误信息等
- 支持日志轮转

### 监控指标
- 用户请求数量
- 查询成功/失败率
- 文件生成大小
- 响应时间

### 告警设置
- 连接失败告警
- 文件大小超限告警
- 请求频率异常告警

## 故障排除

### 常见问题

#### 1. 机器人无响应
```bash
# 检查机器人进程
ps aux | grep start_bot.py

# 查看日志
tail -f bot.log

# 重启机器人
python start_bot.py
```

#### 2. 权限被拒绝
- 检查用户ID是否在允许列表中
- 确认 `.env.bot` 配置正确
- 验证Bot Token有效性

#### 3. 数据库连接失败
- 检查主配置文件 `.env`
- 测试SSH隧道连接
- 验证数据库凭据

#### 4. 文件发送失败
- 检查文件大小是否超限
- 确认临时目录权限
- 验证网络连接

### 调试模式
```bash
# 启用详细日志
export DEBUG=1
python start_bot.py

# 测试配置
python setup_bot.py test

# 检查依赖
python setup_bot.py install
```

## 安全建议

1. **保护Bot Token**：
   - 不要在代码中硬编码Token
   - 使用环境变量或配置文件
   - 定期更换Token

2. **用户权限控制**：
   - 只授权必要的用户
   - 定期审查用户列表
   - 监控异常操作

3. **数据安全**：
   - 及时清理临时文件
   - 加密敏感配置
   - 限制文件访问权限

4. **网络安全**：
   - 使用HTTPS连接
   - 配置防火墙规则
   - 监控网络流量

## 扩展功能

### 计划中的功能
- 📊 数据可视化图表
- 📧 邮件通知集成
- 📅 定时任务调度
- 📈 统计报表生成
- 🔔 实时告警推送

### 自定义开发
机器人采用模块化设计，可以轻松扩展：
- 添加新的命令处理器
- 集成其他数据源
- 自定义输出格式
- 增加业务逻辑

## 技术支持

如有问题请：
1. 查看日志文件
2. 运行测试命令
3. 检查配置文件
4. 联系技术支持团队
