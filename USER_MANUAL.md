# CDN统计查询工具 - 用户手册

## 概述

CDN统计查询工具是一个用于查询和导出CDN统计数据的命令行工具，支持MongoDB数据查询、MySQL用户信息关联、SSH隧道连接等功能。

## 安装方式

### 方式1: 一键安装脚本（推荐）

```bash
# 下载并运行安装脚本
curl -sSL https://your-domain.com/install.sh | bash

# 或者手动下载
wget https://your-domain.com/install.sh
chmod +x install.sh
./install.sh
```

### 方式2: 使用预编译包

1. 下载对应平台的压缩包
2. 解压到任意目录
3. 运行可执行文件

```bash
# Linux/macOS
./cdn-query --help

# Windows
cdn-query.exe --help
```

### 方式3: Python包安装

```bash
pip install cdn-stats-tool
```

### 方式4: Docker运行

```bash
docker run -it --rm cdn-stats-tool
```

## 快速开始

### 1. 配置数据库连接

```bash
# 使用配置向导
cdn-setup setup

# 或者手动编辑配置文件
cp .env.example .env
# 编辑 .env 文件
```

### 2. 测试连接

```bash
# 测试SSH隧道
cdn-query test-ssh

# 测试MongoDB连接
cdn-query test-connection

# 测试MySQL连接
cdn-query test-mysql
```

### 3. 查询数据

```bash
# 查询指定日期的数据
cdn-query query -s 2025-05-20

# 查询日期范围
cdn-query query -s 2025-05-20 -e 2025-05-25

# 导出到CSV
cdn-query query -s 2025-05-20 -o result.csv
```

### 4. 批量导出

```bash
# 从5月20日到昨天的所有数据
cdn-batch-export

# 自定义日期范围
cdn-batch-export -s 2025-05-20 -e 2025-05-30
```

## 详细功能

### 数据查询

#### 基本查询
```bash
# 查询单天数据
cdn-query query -s 2025-05-20

# 查询多天数据
cdn-query query -s 2025-05-20 -e 2025-05-25

# 限制返回记录数
cdn-query query -s 2025-05-20 -l 100

# 不显示用户邮箱
cdn-query query -s 2025-05-20 --no-email
```

#### 输出格式
```bash
# 表格格式（默认）
cdn-query query -s 2025-05-20 -f table

# JSON格式
cdn-query query -s 2025-05-20 -f json

# 导出CSV
cdn-query query -s 2025-05-20 -o data.csv
```

### 批量导出

#### 基本用法
```bash
# 默认从5月20日到昨天
cdn-batch-export

# 指定日期范围
cdn-batch-export -s 2025-05-20 -e 2025-05-30

# 指定输出目录
cdn-batch-export -o /path/to/exports

# 设置查询间隔（秒）
cdn-batch-export -d 2
```

#### 输出结果
```
exports_20241220_143022/
├── cdn_stats_2025-05-20.csv
├── cdn_stats_2025-05-21.csv
├── ...
└── export_summary.txt
```

### 配置管理

#### 数据库配置
```bash
# 配置向导
cdn-setup setup

# SSH隧道配置
cdn-ssh-setup setup

# 查看当前配置
cdn-setup show
```

#### 配置文件
```env
# MongoDB配置
MONGODB_URI=mongodb://localhost:27017/
DATABASE_NAME=your_database
COLLECTION_NAME=cdn_statistics_total_metric

# SSH隧道配置
USE_SSH_TUNNEL=true
SSH_HOST=your-server.com
SSH_USERNAME=username
SSH_KEY_FILE=/path/to/key

# MySQL配置
ENABLE_USER_EMAIL_LOOKUP=true
MYSQL_HOST=localhost
MYSQL_PORT=3307
MYSQL_USERNAME=readonly_user
MYSQL_PASSWORD=password
MYSQL_DATABASE=user_db
```

### 测试和调试

#### 连接测试
```bash
# 测试所有连接
cdn-query test-connection
cdn-query test-mysql
cdn-query test-ssh

# 双隧道测试
cdn-query test-dual-tunnel
```

#### 调试工具
```bash
# 调试用户ID格式
cdn-query debug-user-id

# 调试邮箱查询
cdn-query debug-email
```

## 高级用法

### 自动化脚本

#### 定时任务
```bash
# 编辑crontab
crontab -e

# 每天凌晨2点导出前一天数据
0 2 * * * /usr/local/bin/cdn-batch-export -s $(date -d "yesterday" +\%Y-\%m-\%d) -e $(date -d "yesterday" +\%Y-\%m-\%d)
```

#### 批处理脚本
```bash
#!/bin/bash
# auto_export.sh

# 设置日期范围
START_DATE="2025-05-20"
END_DATE=$(date -d "yesterday" +%Y-%m-%d)

# 执行导出
cdn-batch-export -s "$START_DATE" -e "$END_DATE" -o "/backup/cdn_exports"

# 压缩结果
tar -czf "/backup/cdn_exports_$(date +%Y%m%d).tar.gz" /backup/cdn_exports
```

### Docker使用

#### 基本运行
```bash
# 交互式运行
docker run -it --rm \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/exports:/app/exports \
  cdn-stats-tool

# 批量导出
docker run --rm \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/exports:/app/exports \
  cdn-stats-tool python batch_export.py
```

#### Docker Compose
```yaml
version: '3.8'
services:
  cdn-tool:
    image: cdn-stats-tool
    volumes:
      - ./.env:/app/.env:ro
      - ./exports:/app/exports
    command: python batch_export.py
```

### 数据处理

#### 合并CSV文件
```bash
# 合并所有CSV文件
head -1 exports/cdn_stats_2025-05-20.csv > merged.csv
tail -n +2 exports/*.csv >> merged.csv
```

#### 数据分析
```python
import pandas as pd
import glob

# 读取所有CSV文件
files = glob.glob('exports/*.csv')
df = pd.concat([pd.read_csv(f) for f in files])

# 统计分析
print(df.groupby('用户邮箱')['总流量(字节)'].sum().sort_values(ascending=False))
```

## 故障排除

### 常见问题

#### 1. 连接失败
```bash
# 检查网络连接
ping your-server.com

# 检查SSH连接
ssh <EMAIL>

# 检查端口
telnet your-server.com 22
```

#### 2. 认证失败
```bash
# 检查SSH密钥权限
chmod 600 ~/.ssh/id_rsa

# 检查配置文件
cat .env | grep -E "(USERNAME|PASSWORD|KEY)"
```

#### 3. 数据查询问题
```bash
# 检查数据库连接
cdn-query test-connection

# 检查用户ID格式
cdn-query debug-user-id

# 检查时间范围
cdn-query query -s 2025-05-20 -l 1
```

### 日志和调试

#### 启用详细日志
```bash
# 设置环境变量
export PYTHONUNBUFFERED=1
export DEBUG=1

# 运行工具
cdn-query query -s 2025-05-20
```

#### 保存日志
```bash
# 保存到文件
cdn-batch-export 2>&1 | tee export.log

# 只保存错误
cdn-batch-export 2> error.log
```

## 性能优化

### 大数据量处理
```bash
# 分批导出
cdn-batch-export -s 2025-05-20 -e 2025-05-25 -d 2

# 限制记录数
cdn-query query -s 2025-05-20 -l 1000

# 不查询邮箱
cdn-query query -s 2025-05-20 --no-email
```

### 网络优化
```bash
# 增加查询间隔
cdn-batch-export -d 5

# 使用压缩
ssh -C <EMAIL>
```

## 安全建议

1. **使用SSH密钥认证**而不是密码
2. **设置配置文件权限**：`chmod 600 .env`
3. **不要在版本控制中包含配置文件**
4. **定期更换密码和密钥**
5. **使用只读数据库用户**

## 支持和反馈

如果遇到问题或有建议，请：

1. 查看日志文件
2. 运行诊断命令
3. 检查网络连接
4. 联系技术支持

## 更新和维护

### 更新工具
```bash
# pip安装的更新
pip install --upgrade cdn-stats-tool

# 便携版更新
# 下载新版本并替换文件
```

### 备份配置
```bash
# 备份配置文件
cp .env .env.backup

# 备份导出数据
tar -czf exports_backup.tar.gz exports/
```
