#!/usr/bin/env python3
"""
双SSH隧道测试脚本
测试MongoDB和MySQL的SSH隧道连接
"""

from ssh_tunnel_manager import SSHTunnelManager
from mysql_manager import MySQLManager
from config import Config
import time


def test_dual_ssh_tunnels():
    """测试双SSH隧道功能"""
    print("=== 双SSH隧道测试 ===")
    
    config = Config.get_ssh_config()
    
    if not config['use_ssh_tunnel']:
        print("❌ SSH隧道未启用")
        return False
    
    tunnel_manager = None
    try:
        # 创建SSH隧道管理器
        tunnel_manager = SSHTunnelManager()
        
        print("正在创建SSH隧道...")
        if not tunnel_manager.create_tunnel():
            print("❌ SSH隧道创建失败")
            return False
        
        print("✅ SSH隧道创建成功")
        
        # 测试MongoDB隧道
        print("\n--- 测试MongoDB隧道 ---")
        mongodb_uri = tunnel_manager.get_local_mongodb_uri()
        print(f"MongoDB连接URI: {mongodb_uri[:50]}...")
        
        from pymongo import MongoClient
        try:
            client = MongoClient(mongodb_uri)
            client.admin.command('ping')
            print("✅ MongoDB隧道连接成功")
            
            # 测试数据库访问
            db_config = Config.get_mongodb_config()
            if db_config['database']:
                db = client[db_config['database']]
                collections = db.list_collection_names()
                print(f"可用集合: {collections[:5]}...")  # 只显示前5个
            
            client.close()
        except Exception as e:
            print(f"❌ MongoDB隧道连接失败: {e}")
            return False
        
        # 测试MySQL隧道
        mysql_config = Config.get_mysql_config()
        if mysql_config['enable_lookup']:
            print("\n--- 测试MySQL隧道 ---")
            mysql_tunnel_config = tunnel_manager.get_local_mysql_config()
            print(f"MySQL隧道配置: {mysql_tunnel_config}")
            
            try:
                mysql_manager = MySQLManager(tunnel_manager=tunnel_manager)
                if mysql_manager.test_connection():
                    print("✅ MySQL隧道连接成功")
                    
                    # 测试用户查询
                    user_count = mysql_manager.get_user_count()
                    print(f"用户表记录数: {user_count}")
                    
                    # 测试单个用户查询
                    test_user_id = "2796484965376"
                    email = mysql_manager.get_user_email(test_user_id)
                    if email:
                        print(f"测试用户查询成功: {test_user_id} -> {email}")
                    else:
                        print(f"未找到测试用户: {test_user_id}")
                else:
                    print("❌ MySQL隧道连接失败")
                    return False
                
                mysql_manager.close_connection()
            except Exception as e:
                print(f"❌ MySQL隧道测试失败: {e}")
                return False
        else:
            print("\n--- MySQL查询功能未启用 ---")
        
        print("\n✅ 双SSH隧道测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 双SSH隧道测试失败: {e}")
        return False
    finally:
        if tunnel_manager:
            tunnel_manager.close_tunnel()


def test_integrated_query():
    """测试集成查询功能"""
    print("\n=== 集成查询测试 ===")
    
    try:
        from cdn_query_tool import CDNQueryTool
        from datetime import datetime, timedelta
        
        # 创建查询工具
        tool = CDNQueryTool()
        
        # 模拟查询结果进行测试
        mock_results = [
            {"_id": "2796484965376", "total_flux": 1073741824},
            {"_id": "2796484965377", "total_flux": 536870912},
            {"_id": "test_user_123", "total_flux": 268435456}
        ]
        
        print("测试结果格式化（包含邮箱查询）...")
        formatted_results = tool.format_results(mock_results, show_email=True)
        
        print("格式化结果:")
        for result in formatted_results:
            print(f"  排名: {result['排名']}")
            print(f"  用户ID: {result['用户ID']}")
            if '用户邮箱' in result:
                print(f"  用户邮箱: {result['用户邮箱']}")
            print(f"  总流量: {result['总流量(可读)']}")
            print()
        
        tool.close_connection()
        print("✅ 集成查询测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 集成查询测试失败: {e}")
        return False


def show_tunnel_info():
    """显示隧道配置信息"""
    print("\n=== 隧道配置信息 ===")
    
    ssh_config = Config.get_ssh_config()
    mysql_config = Config.get_mysql_config()
    
    print(f"SSH服务器: {ssh_config['ssh_host']}:{ssh_config['ssh_port']}")
    print(f"SSH用户: {ssh_config['ssh_username']}")
    
    print(f"\nMongoDB隧道:")
    print(f"  远程: {ssh_config['remote_mongodb_host']}:{ssh_config['remote_mongodb_port']}")
    print(f"  本地: localhost:{ssh_config['local_tunnel_port']}")
    
    if mysql_config['enable_lookup']:
        print(f"\nMySQL隧道:")
        print(f"  远程: {ssh_config['remote_mysql_host']}:{ssh_config['remote_mysql_port']}")
        print(f"  本地: localhost:{ssh_config['local_mysql_tunnel_port']}")
    else:
        print(f"\nMySQL隧道: 未启用")


def main():
    """主函数"""
    print("双SSH隧道测试工具")
    print("=" * 50)
    
    # 显示配置信息
    show_tunnel_info()
    
    # 运行测试
    tests = [
        ("双SSH隧道连接", test_dual_ssh_tunnels),
        ("集成查询功能", test_integrated_query)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！双SSH隧道配置正确。")
    else:
        print("⚠️  部分测试失败，请检查配置。")
        print("\n建议检查项目:")
        print("1. SSH服务器连接配置")
        print("2. MongoDB和MySQL服务器地址")
        print("3. 认证信息是否正确")
        print("4. 网络连接是否正常")


if __name__ == "__main__":
    main()
