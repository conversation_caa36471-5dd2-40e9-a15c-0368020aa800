#!/usr/bin/env python3
"""
MongoDB配置助手
帮助配置MongoDB连接信息
"""

import click
import os
from urllib.parse import quote_plus


def create_mongodb_config():
    """创建MongoDB配置"""
    click.echo("=== MongoDB配置助手 ===\n")
    
    # 基本信息
    click.echo("检测到您的MongoDB连接信息:")
    click.echo("主机: *************:27017")
    click.echo("用户: mongouser")
    click.echo("认证数据库: admin")
    
    # 获取密码
    mongodb_password = click.prompt('\n请输入MongoDB密码', hide_input=True)
    
    # URL编码密码（处理特殊字符）
    encoded_password = quote_plus(mongodb_password)
    
    # 构建MongoDB URI
    mongodb_uri = f"mongodb://mongouser:{encoded_password}@*************:27017/?authSource=admin"
    
    # 获取数据库和集合信息
    click.echo("\n--- 数据库配置 ---")
    database_name = click.prompt('数据库名称')
    collection_name = click.prompt('集合名称', default='cdn_statistics_total_metric')
    
    # MySQL配置
    click.echo("\n--- MySQL配置（用于查询用户邮箱） ---")
    enable_mysql = click.confirm('是否启用用户邮箱查询功能？', default=True)
    
    mysql_config = {}
    if enable_mysql:
        mysql_config['host'] = click.prompt('MySQL主机地址', default='*************')
        mysql_config['port'] = click.prompt('MySQL端口', default=3306, type=int)
        mysql_config['username'] = click.prompt('MySQL用户名')
        mysql_config['password'] = click.prompt('MySQL密码', hide_input=True)
        mysql_config['database'] = click.prompt('MySQL数据库名')
        mysql_config['table'] = click.prompt('用户表名', default='vc_user')
        mysql_config['id_field'] = click.prompt('用户ID字段名', default='id')
        mysql_config['email_field'] = click.prompt('用户邮箱字段名', default='email')
    
    # 生成配置文件内容
    config_content = f"""# MongoDB 配置（直接连接）
MONGODB_URI={mongodb_uri}
DATABASE_NAME={database_name}
COLLECTION_NAME={collection_name}

# SSH 隧道配置（当前不使用）
USE_SSH_TUNNEL=false
SSH_HOST=
SSH_PORT=22
SSH_USERNAME=
SSH_PASSWORD=
SSH_KEY_FILE=

# 远程MongoDB配置（SSH隧道模式下使用）
REMOTE_MONGODB_HOST=localhost
REMOTE_MONGODB_PORT=27017

# 本地隧道端口
LOCAL_TUNNEL_PORT=27018

# MySQL 配置（用于查询用户email）
ENABLE_USER_EMAIL_LOOKUP={str(enable_mysql).lower()}"""

    if enable_mysql:
        config_content += f"""
MYSQL_HOST={mysql_config['host']}
MYSQL_PORT={mysql_config['port']}
MYSQL_USERNAME={mysql_config['username']}
MYSQL_PASSWORD={mysql_config['password']}
MYSQL_DATABASE={mysql_config['database']}
MYSQL_USER_TABLE={mysql_config['table']}
MYSQL_USER_ID_FIELD={mysql_config['id_field']}
MYSQL_USER_EMAIL_FIELD={mysql_config['email_field']}"""
    else:
        config_content += """
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USERNAME=
MYSQL_PASSWORD=
MYSQL_DATABASE=
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email"""

    config_content += """

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai
"""

    # 写入配置文件
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    click.echo(f"\n✅ 配置文件已保存到 .env")
    
    # 显示下一步操作
    click.echo("\n--- 下一步操作 ---")
    click.echo("1. 测试MongoDB连接: python main.py test-connection")
    if enable_mysql:
        click.echo("2. 测试MySQL连接: python main.py test-mysql")
    click.echo("3. 开始查询数据: python main.py query -s 2025-05-20")


def test_mongodb_connection():
    """测试MongoDB连接"""
    click.echo("=== 测试MongoDB连接 ===")
    
    try:
        from config import Config
        from pymongo import MongoClient
        
        config = Config.get_mongodb_config()
        click.echo(f"连接URI: {config['uri'][:50]}...")
        
        client = MongoClient(config['uri'])
        
        # 测试连接
        client.admin.command('ping')
        click.echo("✅ MongoDB连接成功!")
        
        # 显示数据库信息
        db_names = client.list_database_names()
        click.echo(f"可用数据库: {db_names}")
        
        # 测试目标数据库
        if config['database'] in db_names:
            db = client[config['database']]
            collections = db.list_collection_names()
            click.echo(f"数据库 '{config['database']}' 中的集合: {collections}")
            
            # 测试目标集合
            if config['collection'] in collections:
                collection = db[config['collection']]
                count = collection.estimated_document_count()
                click.echo(f"集合 '{config['collection']}' 文档数量: {count}")
                
                # 显示一个示例文档
                sample = collection.find_one()
                if sample:
                    click.echo("示例文档结构:")
                    for key in sample.keys():
                        click.echo(f"  - {key}: {type(sample[key]).__name__}")
            else:
                click.echo(f"⚠️  集合 '{config['collection']}' 不存在")
        else:
            click.echo(f"⚠️  数据库 '{config['database']}' 不存在")
        
        client.close()
        return True
        
    except Exception as e:
        click.echo(f"❌ MongoDB连接失败: {e}")
        return False


@click.group()
def cli():
    """MongoDB配置助手"""
    pass


@cli.command()
def setup():
    """配置MongoDB连接"""
    create_mongodb_config()


@cli.command()
def test():
    """测试MongoDB连接"""
    if not os.path.exists('.env'):
        click.echo("❌ .env文件不存在，请先运行 setup 命令")
        return
    
    test_mongodb_connection()


@cli.command()
def show():
    """显示当前配置"""
    if not os.path.exists('.env'):
        click.echo("❌ .env文件不存在")
        return
    
    click.echo("=== 当前配置 ===")
    with open('.env', 'r', encoding='utf-8') as f:
        content = f.read()
        # 隐藏密码
        lines = content.split('\n')
        for line in lines:
            if 'PASSWORD' in line and '=' in line:
                key, value = line.split('=', 1)
                if value:
                    click.echo(f"{key}=***")
                else:
                    click.echo(line)
            else:
                click.echo(line)


if __name__ == '__main__':
    cli()
