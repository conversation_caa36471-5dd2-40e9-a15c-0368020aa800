#!/usr/bin/env python3
"""
快速测试邮箱查询功能
"""

from cdn_query_tool import CDNQueryTool
from datetime import datetime, timedelta


def test_email_query():
    """测试邮箱查询功能"""
    print("=== 快速邮箱查询测试 ===")
    
    try:
        # 创建查询工具
        tool = CDNQueryTool()
        
        # 查询昨天的数据（少量）
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        print(f"查询日期: {yesterday}")
        
        results = tool.query_flux_by_date_range(
            start_date=yesterday,
            limit=3  # 只查询3条记录进行测试
        )
        
        if results:
            print(f"\n查询到 {len(results)} 条记录")
            
            # 测试格式化结果（包含邮箱查询）
            print("\n--- 测试邮箱查询 ---")
            formatted_results = tool.format_results(results, show_email=True)
            
            print("\n格式化结果:")
            for result in formatted_results:
                print(f"排名: {result['排名']}")
                print(f"用户ID: {result['用户ID']}")
                if '用户邮箱' in result:
                    print(f"用户邮箱: {result['用户邮箱']}")
                print(f"总流量: {result['总流量(可读)']}")
                print("-" * 30)
            
            # 测试不显示邮箱
            print("\n--- 测试不显示邮箱 ---")
            formatted_results_no_email = tool.format_results(results, show_email=False)
            
            for result in formatted_results_no_email:
                print(f"排名: {result['排名']}, 用户ID: {result['用户ID']}, 流量: {result['总流量(可读)']}")
        
        else:
            print("没有查询到数据，可能需要调整查询日期")
        
        tool.close_connection()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_user_id_format():
    """测试用户ID格式处理"""
    print("\n=== 用户ID格式处理测试 ===")
    
    # 模拟不同格式的用户ID
    test_user_ids = [
        2796484965376,           # 整数
        2.796484965376e+12,      # 科学计数法浮点数
        "2796484965376",         # 字符串
        "2.796484965376e+12",    # 科学计数法字符串
        1234567890,              # 普通整数
        "test_user_123"          # 字符串ID
    ]
    
    print("测试用户ID格式转换:")
    for user_id in test_user_ids:
        print(f"\n原始值: {user_id} (类型: {type(user_id).__name__})")
        
        # 应用我们的转换逻辑
        if isinstance(user_id, (int, float)):
            user_id_str = str(int(user_id))
        else:
            user_id_str = str(user_id)
            if 'e' in user_id_str.lower():
                try:
                    user_id_str = str(int(float(user_id_str)))
                    print(f"  科学计数法转换: {user_id} -> {user_id_str}")
                except:
                    print(f"  无法转换科学计数法: {user_id}")
        
        print(f"最终结果: {user_id_str}")


if __name__ == "__main__":
    print("快速邮箱查询测试工具")
    print("=" * 50)
    
    # 测试用户ID格式处理
    test_user_id_format()
    
    # 测试实际邮箱查询
    test_email_query()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("\n如果仍然无法查询到邮箱，请运行:")
    print("python debug_user_id.py  # 详细调试")
    print("python main.py test-mysql  # 测试MySQL连接")
