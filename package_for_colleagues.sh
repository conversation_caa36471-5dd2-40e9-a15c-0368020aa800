#!/bin/bash

# 为同事打包CDN统计查询工具
# 创建易于分发和使用的软件包

set -e

echo "🚀 为同事打包CDN统计查询工具"
echo "================================"

# 检查必要文件
check_files() {
    echo "📋 检查必要文件..."
    
    required_files=(
        "main.py"
        "cdn_query_tool.py"
        "mysql_manager.py"
        "ssh_tunnel_manager.py"
        "config.py"
        "batch_export.py"
        "requirements.txt"
        ".env.example"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            echo "❌ 缺少文件: $file"
            exit 1
        fi
    done
    
    echo "✅ 所有必要文件都存在"
}

# 创建分发包
create_distribution() {
    local dist_name="cdn-stats-tool-v1.0"
    local dist_dir="$dist_name"
    
    echo "📦 创建分发包: $dist_name"
    
    # 清理旧的分发目录
    if [ -d "$dist_dir" ]; then
        rm -rf "$dist_dir"
    fi
    
    # 创建分发目录
    mkdir -p "$dist_dir"
    
    # 复制核心文件
    echo "📄 复制核心文件..."
    cp *.py "$dist_dir/"
    cp requirements.txt "$dist_dir/"
    cp .env.example "$dist_dir/"
    cp .env.ssh_template "$dist_dir/"
    
    # 复制文档
    echo "📚 复制文档文件..."
    cp README.md "$dist_dir/" 2>/dev/null || echo "README.md not found"
    cp USER_MANUAL.md "$dist_dir/" 2>/dev/null || echo "USER_MANUAL.md not found"
    cp BATCH_EXPORT_GUIDE.md "$dist_dir/" 2>/dev/null || echo "BATCH_EXPORT_GUIDE.md not found"
    cp SSH_TUNNEL_SETUP.md "$dist_dir/" 2>/dev/null || echo "SSH_TUNNEL_SETUP.md not found"
    
    # 复制脚本
    echo "🔧 复制脚本文件..."
    cp *.sh "$dist_dir/" 2>/dev/null || echo "No shell scripts found"
    
    # 设置脚本权限
    chmod +x "$dist_dir"/*.sh 2>/dev/null || true
    
    return 0
}

# 创建安装脚本
create_installer() {
    local dist_dir=$1
    
    echo "🛠️ 创建安装脚本..."
    
    # Windows安装脚本
    cat > "$dist_dir/install.bat" << 'EOF'
@echo off
echo CDN统计查询工具 - Windows安装程序
echo ===================================
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 正在安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)

echo.
echo ✅ 安装完成！
echo.
echo 使用方法:
echo   python main.py --help          查看帮助
echo   python setup_wizard.py setup   配置数据库
echo   python main.py query -s 2025-05-20  查询数据
echo   python batch_export.py         批量导出
echo.
pause
EOF

    # Linux/macOS安装脚本
    cat > "$dist_dir/install.sh" << 'EOF'
#!/bin/bash

echo "CDN统计查询工具 - Linux/macOS安装程序"
echo "===================================="
echo ""

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3"
    echo "请先安装Python 3.8或更高版本"
    exit 1
fi

echo "✅ Python版本: $(python3 --version)"

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ 错误: 未找到pip3"
    exit 1
fi

echo "📦 正在安装依赖包..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败，请检查网络连接"
    exit 1
fi

echo ""
echo "✅ 安装完成！"
echo ""
echo "使用方法:"
echo "  python3 main.py --help              查看帮助"
echo "  python3 setup_wizard.py setup       配置数据库"
echo "  python3 main.py query -s 2025-05-20 查询数据"
echo "  python3 batch_export.py             批量导出"
echo ""
EOF

    chmod +x "$dist_dir/install.sh"
}

# 创建快速启动脚本
create_launchers() {
    local dist_dir=$1
    
    echo "🚀 创建快速启动脚本..."
    
    # Windows启动脚本
    cat > "$dist_dir/start.bat" << 'EOF'
@echo off
echo CDN统计查询工具
echo ===============
echo.
echo 请选择操作:
echo 1. 配置数据库连接
echo 2. 查询指定日期数据
echo 3. 批量导出数据
echo 4. 测试连接
echo 5. 查看帮助
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" (
    python setup_wizard.py setup
) else if "%choice%"=="2" (
    set /p date="请输入日期 (YYYY-MM-DD): "
    python main.py query -s %date%
) else if "%choice%"=="3" (
    python batch_export.py
) else if "%choice%"=="4" (
    python main.py test-connection
) else if "%choice%"=="5" (
    python main.py --help
) else (
    echo 无效选择
)

pause
EOF

    # Linux/macOS启动脚本
    cat > "$dist_dir/start.sh" << 'EOF'
#!/bin/bash

echo "CDN统计查询工具"
echo "==============="
echo ""
echo "请选择操作:"
echo "1. 配置数据库连接"
echo "2. 查询指定日期数据"
echo "3. 批量导出数据"
echo "4. 测试连接"
echo "5. 查看帮助"
echo ""
read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        python3 setup_wizard.py setup
        ;;
    2)
        read -p "请输入日期 (YYYY-MM-DD): " date
        python3 main.py query -s "$date"
        ;;
    3)
        python3 batch_export.py
        ;;
    4)
        python3 main.py test-connection
        ;;
    5)
        python3 main.py --help
        ;;
    *)
        echo "无效选择"
        ;;
esac
EOF

    chmod +x "$dist_dir/start.sh"
}

# 创建使用说明
create_readme() {
    local dist_dir=$1
    
    echo "📖 创建使用说明..."
    
    cat > "$dist_dir/README_QUICK_START.md" << 'EOF'
# CDN统计查询工具 - 快速开始

## 安装

### Windows用户
1. 双击运行 `install.bat`
2. 等待安装完成

### Linux/macOS用户
```bash
chmod +x install.sh
./install.sh
```

## 快速使用

### 方式1: 图形化菜单
- Windows: 双击 `start.bat`
- Linux/macOS: 运行 `./start.sh`

### 方式2: 命令行
```bash
# 1. 配置数据库连接
python setup_wizard.py setup

# 2. 查询数据
python main.py query -s 2025-05-20

# 3. 批量导出
python batch_export.py
```

## 主要功能

1. **数据查询**: 查询指定日期的CDN统计数据
2. **批量导出**: 批量导出多天数据到CSV文件
3. **用户邮箱**: 自动关联显示用户邮箱信息
4. **SSH隧道**: 支持通过SSH隧道连接远程数据库

## 配置说明

工具需要连接两个数据库:
- **MongoDB**: 存储CDN统计数据
- **MySQL**: 存储用户邮箱信息

支持通过SSH隧道连接远程数据库。

## 常用命令

```bash
# 查看帮助
python main.py --help

# 配置数据库
python setup_wizard.py setup

# 查询单天数据
python main.py query -s 2025-05-20

# 查询并导出CSV
python main.py query -s 2025-05-20 -o result.csv

# 批量导出(从5月20日到昨天)
python batch_export.py

# 测试连接
python main.py test-connection
python main.py test-mysql
```

## 故障排除

1. **Python未安装**: 请安装Python 3.8或更高版本
2. **依赖安装失败**: 检查网络连接，或使用国内镜像源
3. **数据库连接失败**: 检查配置文件和网络连接
4. **SSH连接失败**: 检查SSH配置和密钥权限

## 技术支持

如有问题请联系技术支持团队。
EOF
}

# 创建压缩包
create_archive() {
    local dist_dir=$1
    local archive_name="${dist_dir}.zip"
    
    echo "📦 创建压缩包: $archive_name"
    
    # 创建ZIP压缩包
    if command -v zip &> /dev/null; then
        zip -r "$archive_name" "$dist_dir"
    else
        echo "⚠️  zip命令不可用，跳过压缩包创建"
        return 1
    fi
    
    echo "✅ 压缩包创建完成: $archive_name"
    echo "📊 文件大小: $(du -h "$archive_name" | cut -f1)"
}

# 显示分发信息
show_distribution_info() {
    local dist_dir=$1
    
    echo ""
    echo "🎉 打包完成！"
    echo "=============="
    echo ""
    echo "📁 分发目录: $dist_dir"
    echo "📦 压缩包: ${dist_dir}.zip (如果创建成功)"
    echo ""
    echo "📋 分发内容:"
    ls -la "$dist_dir"
    echo ""
    echo "💡 分发给同事的方法:"
    echo "1. 将整个 '$dist_dir' 目录复制给同事"
    echo "2. 或者发送 '${dist_dir}.zip' 压缩包"
    echo "3. 同事解压后运行 install.bat (Windows) 或 install.sh (Linux/macOS)"
    echo "4. 然后运行 start.bat 或 start.sh 开始使用"
    echo ""
    echo "📖 使用说明:"
    echo "- 详细说明请查看 $dist_dir/README_QUICK_START.md"
    echo "- 完整手册请查看 $dist_dir/USER_MANUAL.md"
}

# 主函数
main() {
    # 检查文件
    check_files
    
    # 创建分发包
    local dist_name="cdn-stats-tool-v1.0"
    create_distribution
    
    # 创建安装脚本
    create_installer "$dist_name"
    
    # 创建启动脚本
    create_launchers "$dist_name"
    
    # 创建说明文档
    create_readme "$dist_name"
    
    # 创建压缩包
    create_archive "$dist_name"
    
    # 显示信息
    show_distribution_info "$dist_name"
}

# 运行主函数
main "$@"
