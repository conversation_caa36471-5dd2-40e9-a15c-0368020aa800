# Git相关
.git
.gitignore

# Python相关
__pycache__
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache
.coverage

# 虚拟环境
venv/
env/
.venv/
.env/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 临时文件
temp/
tmp/
.tmp/

# 导出文件
exports/
*.csv
*.zip

# 配置文件（这些应该通过挂载提供）
.env
.env.bot
.env.local

# 系统文件
.DS_Store
Thumbs.db

# 构建产物
build/
dist/
*.egg-info/

# 文档（保留README.md）
*.md
!README.md

# SSH密钥（应该通过挂载提供）
*.pem
*.key
id_rsa*

# 其他
*.bak
*.backup
