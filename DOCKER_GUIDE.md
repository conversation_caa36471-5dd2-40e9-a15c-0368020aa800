# CDN统计查询工具 - Docker部署指南

## 🐳 Docker部署

### 快速开始

#### 1. 准备配置文件
确保您已经有以下配置文件：
- `.env` - 数据库配置
- `.env.bot` - Telegram机器人配置

#### 2. 构建并启动
```bash
# 使用docker-compose启动（推荐）
docker-compose up -d

# 或者手动构建和运行
docker build -t cdn-stats-tool .
docker run -d \
  --name cdn-stats-bot \
  -v $(pwd)/.env:/app/.env:ro \
  -v $(pwd)/.env.bot:/app/.env.bot:ro \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/exports:/app/exports \
  cdn-stats-tool
```

## 📋 部署方式

### 方式1: Docker Compose（推荐）

#### 启动机器人
```bash
docker-compose up -d
```

#### 查看日志
```bash
docker-compose logs -f cdn-stats-bot
```

#### 停止服务
```bash
docker-compose down
```

#### 重启服务
```bash
docker-compose restart cdn-stats-bot
```

### 方式2: 直接使用Docker

#### 构建镜像
```bash
docker build -t cdn-stats-tool .
```

#### 运行机器人
```bash
docker run -d \
  --name cdn-stats-bot \
  --restart unless-stopped \
  -v $(pwd)/.env:/app/.env:ro \
  -v $(pwd)/.env.bot:/app/.env.bot:ro \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/exports:/app/exports \
  -e TZ=Asia/Shanghai \
  cdn-stats-tool
```

#### 运行命令行工具
```bash
docker run -it --rm \
  -v $(pwd)/.env:/app/.env:ro \
  -v $(pwd)/exports:/app/exports \
  -e START_MODE=cli \
  cdn-stats-tool \
  python main.py query -s 2025-05-20
```

## 🔧 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `START_MODE` | `bot` | 启动模式：bot/cli/test |
| `PYTHONUNBUFFERED` | `1` | Python输出缓冲 |
| `TZ` | `Asia/Shanghai` | 时区设置 |

### 启动模式

#### 机器人模式（默认）
```bash
docker run -e START_MODE=bot cdn-stats-tool
```

#### 命令行模式
```bash
docker run -e START_MODE=cli cdn-stats-tool python main.py --help
```

#### 测试模式
```bash
docker run -e START_MODE=test cdn-stats-tool
```

## 📁 数据挂载

### 必需挂载
- `.env` → `/app/.env` - 数据库配置
- `.env.bot` → `/app/.env.bot` - 机器人配置

### 可选挂载
- `./logs` → `/app/logs` - 日志文件
- `./exports` → `/app/exports` - 导出文件
- `./temp` → `/app/temp` - 临时文件
- `~/.ssh` → `/root/.ssh` - SSH密钥（如果使用SSH隧道）

## 🔐 SSH密钥配置

如果使用SSH隧道，需要挂载SSH密钥：

```bash
docker run -d \
  --name cdn-stats-bot \
  -v $(pwd)/.env:/app/.env:ro \
  -v $(pwd)/.env.bot:/app/.env.bot:ro \
  -v ~/.ssh:/root/.ssh:ro \
  cdn-stats-tool
```

或在docker-compose.yml中添加：
```yaml
volumes:
  - ~/.ssh:/root/.ssh:ro
```

## 📊 监控和日志

### 查看容器状态
```bash
docker ps
docker-compose ps
```

### 查看日志
```bash
# 实时日志
docker logs -f cdn-stats-bot
docker-compose logs -f cdn-stats-bot

# 最近日志
docker logs --tail 100 cdn-stats-bot
```

### 进入容器
```bash
docker exec -it cdn-stats-bot bash
docker-compose exec cdn-stats-bot bash
```

### 健康检查
```bash
docker inspect cdn-stats-bot | grep Health -A 10
```

## 🛠️ 故障排除

### 1. 配置文件问题
```bash
# 检查配置文件是否存在
ls -la .env .env.bot

# 检查配置文件内容
docker run --rm -v $(pwd)/.env:/app/.env cdn-stats-tool cat /app/.env
```

### 2. 网络连接问题
```bash
# 测试连接
docker run --rm \
  -v $(pwd)/.env:/app/.env \
  -e START_MODE=test \
  cdn-stats-tool
```

### 3. 权限问题
```bash
# 检查文件权限
ls -la logs/ exports/

# 修复权限
sudo chown -R $USER:$USER logs/ exports/
```

### 4. 容器重启
```bash
# 重启容器
docker restart cdn-stats-bot
docker-compose restart cdn-stats-bot

# 强制重新创建
docker-compose up -d --force-recreate
```

## 🚀 生产环境部署

### 1. 使用外部数据卷
```yaml
version: '3.8'
services:
  cdn-stats-bot:
    # ... 其他配置
    volumes:
      - cdn-logs:/app/logs
      - cdn-exports:/app/exports

volumes:
  cdn-logs:
    driver: local
  cdn-exports:
    driver: local
```

### 2. 资源限制
```yaml
services:
  cdn-stats-bot:
    # ... 其他配置
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
```

### 3. 自动重启策略
```yaml
services:
  cdn-stats-bot:
    restart: unless-stopped
    # 或者
    # restart: always
```

## 📦 镜像管理

### 构建优化镜像
```bash
# 多阶段构建（如果需要）
docker build --target production -t cdn-stats-tool:latest .

# 压缩镜像
docker build --squash -t cdn-stats-tool:latest .
```

### 镜像清理
```bash
# 清理未使用的镜像
docker image prune

# 清理所有未使用的资源
docker system prune -a
```

## 🎯 最佳实践

1. **配置管理**: 使用环境变量和配置文件挂载
2. **数据持久化**: 挂载重要目录到宿主机
3. **日志管理**: 配置日志轮转和大小限制
4. **安全性**: 使用非root用户运行（可选）
5. **监控**: 配置健康检查和日志监控
6. **备份**: 定期备份配置文件和导出数据

现在您可以使用Docker轻松部署CDN统计查询工具了！🐳
