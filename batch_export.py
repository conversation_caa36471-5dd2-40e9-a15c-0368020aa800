#!/usr/bin/env python3
"""
CDN统计数据批量导出工具
从2025-05-20开始到昨天，按日期生成CSV文件
"""

import os
import sys
from datetime import datetime, timedelta, date
from cdn_query_tool import CDNQueryTool
import click


def get_date_range(start_date_str="2025-05-20", end_date_str=None):
    """获取日期范围"""
    start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
    
    if end_date_str:
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    else:
        # 默认到昨天
        end_date = date.today() - timedelta(days=1)
    
    return start_date, end_date


def generate_date_list(start_date, end_date):
    """生成日期列表"""
    date_list = []
    current_date = start_date
    
    while current_date <= end_date:
        date_list.append(current_date.strftime('%Y-%m-%d'))
        current_date += timedelta(days=1)
    
    return date_list


def export_single_day(tool, date_str, output_dir):
    """导出单日数据"""
    try:
        print(f"📊 正在导出 {date_str} 的数据...")
        
        # 查询数据
        results = tool.query_flux_by_date_range(start_date=date_str)
        
        if results:
            # 格式化结果
            formatted_results = tool.format_results(results, show_email=True)
            
            # 导出CSV
            csv_file = os.path.join(output_dir, f"cdn_stats_{date_str}.csv")
            tool.export_to_csv(formatted_results, csv_file)
            
            record_count = len(formatted_results)
            print(f"✅ {date_str}: 导出成功，共 {record_count} 条记录")
            return True, record_count
        else:
            print(f"⚠️  {date_str}: 无数据记录")
            return False, 0
            
    except Exception as e:
        print(f"❌ {date_str}: 导出失败 - {e}")
        return False, 0


def generate_summary_report(output_dir, date_range, success_count, failed_count, total_records):
    """生成汇总报告"""
    summary_file = os.path.join(output_dir, "export_summary.txt")
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("CDN统计数据导出汇总报告\n")
        f.write("=" * 30 + "\n")
        f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"日期范围: {date_range[0]} 到 {date_range[-1]}\n")
        f.write(f"总天数: {len(date_range)}\n")
        f.write(f"成功导出: {success_count} 天\n")
        f.write(f"失败/无数据: {failed_count} 天\n")
        f.write(f"总记录数: {total_records}\n")
        f.write(f"输出目录: {output_dir}\n\n")
        
        f.write("文件列表:\n")
        f.write("-" * 20 + "\n")
        
        # 列出所有CSV文件
        csv_files = [f for f in os.listdir(output_dir) if f.endswith('.csv')]
        csv_files.sort()
        
        for csv_file in csv_files:
            csv_path = os.path.join(output_dir, csv_file)
            try:
                with open(csv_path, 'r', encoding='utf-8') as csv_f:
                    line_count = sum(1 for _ in csv_f) - 1  # 减去标题行
                f.write(f"{csv_file}: {line_count} 条记录\n")
            except:
                f.write(f"{csv_file}: 无法读取\n")
    
    return summary_file


@click.command()
@click.option('--start-date', '-s', default='2025-05-20', 
              help='开始日期，格式：YYYY-MM-DD')
@click.option('--end-date', '-e', default=None,
              help='结束日期，格式：YYYY-MM-DD（默认为昨天）')
@click.option('--output-dir', '-o', default=None,
              help='输出目录（默认为自动生成）')
@click.option('--delay', '-d', default=1, type=int,
              help='每次查询间隔秒数（默认1秒）')
def batch_export(start_date, end_date, output_dir, delay):
    """批量导出CDN统计数据"""
    
    print("🚀 CDN统计数据批量导出工具")
    print("=" * 40)
    
    try:
        # 获取日期范围
        start_dt, end_dt = get_date_range(start_date, end_date)
        date_range = generate_date_list(start_dt, end_dt)
        
        print(f"📅 导出日期范围: {start_dt} 到 {end_dt}")
        print(f"📊 总共需要导出: {len(date_range)} 天")
        
        # 创建输出目录
        if not output_dir:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_dir = f"csv_exports_{timestamp}"
        
        os.makedirs(output_dir, exist_ok=True)
        print(f"📁 输出目录: {output_dir}")
        
        # 创建查询工具实例
        print("\n🔗 正在连接数据库...")
        tool = CDNQueryTool()
        print("✅ 数据库连接成功")
        
        # 批量导出
        print(f"\n📈 开始批量导出...")
        success_count = 0
        failed_count = 0
        total_records = 0
        
        for i, date_str in enumerate(date_range, 1):
            print(f"\n[{i}/{len(date_range)}] ", end="")
            
            success, record_count = export_single_day(tool, date_str, output_dir)
            
            if success:
                success_count += 1
                total_records += record_count
            else:
                failed_count += 1
            
            # 延迟，避免对数据库造成压力
            if delay > 0 and i < len(date_range):
                import time
                time.sleep(delay)
        
        # 关闭数据库连接
        tool.close_connection()
        
        # 生成汇总报告
        print(f"\n📄 正在生成汇总报告...")
        summary_file = generate_summary_report(
            output_dir, date_range, success_count, failed_count, total_records
        )
        
        # 显示最终结果
        print("\n" + "=" * 40)
        print("🎉 批量导出完成！")
        print(f"\n📈 统计结果:")
        print(f"   总天数: {len(date_range)}")
        print(f"   成功导出: {success_count} 天")
        print(f"   失败/无数据: {failed_count} 天")
        print(f"   总记录数: {total_records}")
        print(f"\n📁 输出目录: {output_dir}")
        print(f"📄 汇总报告: {summary_file}")
        
        print(f"\n💡 使用提示:")
        print(f"   查看汇总: cat {summary_file}")
        print(f"   查看文件: ls -la {output_dir}/")
        print(f"   查看某天: cat {output_dir}/cdn_stats_2025-05-20.csv")
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️  用户中断导出")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def quick_export():
    """快速导出（从5月20日到昨天）"""
    print("🚀 快速导出模式：从2025-05-20到昨天")
    
    # 使用默认参数调用批量导出
    from click.testing import CliRunner
    runner = CliRunner()
    result = runner.invoke(batch_export, [])
    
    if result.exit_code == 0:
        print("✅ 快速导出完成")
    else:
        print("❌ 快速导出失败")
        print(result.output)


if __name__ == "__main__":
    # 检查是否有命令行参数
    if len(sys.argv) == 1:
        # 无参数时使用快速导出模式
        quick_export()
    else:
        # 有参数时使用完整的命令行界面
        batch_export()
