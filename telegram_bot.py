#!/usr/bin/env python3
"""
CDN统计查询工具 - Telegram机器人
支持查询数据、批量导出、权限控制等功能
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta, date
from typing import List, Dict, Optional
import tempfile
import shutil

from telegram import Update, BotCommand, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes, MessageHandler, filters
from telegram.constants import ParseMode

from cdn_query_tool import CDNQueryTool
from config import Config


# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)


class CDNTelegramBot:
    """CDN统计查询Telegram机器人"""
    
    def __init__(self, token: str, allowed_users: List[int]):
        """
        初始化机器人
        
        Args:
            token: Telegram Bot Token
            allowed_users: 允许使用机器人的用户ID列表
        """
        self.token = token
        self.allowed_users = set(allowed_users)
        self.application = Application.builder().token(token).build()
        self.setup_handlers()
    
    def setup_handlers(self):
        """设置命令处理器"""
        handlers = [
            CommandHandler("start", self.start_command),
            CommandHandler("help", self.help_command),
            CommandHandler("query", self.query_command),
            CommandHandler("batch", self.batch_command),
            CommandHandler("test", self.test_command),
            CommandHandler("status", self.status_command),
            CallbackQueryHandler(self.button_callback),
        ]
        
        for handler in handlers:
            self.application.add_handler(handler)
        
        # 错误处理
        self.application.add_error_handler(self.error_handler)
    
    def check_permission(self, user_id: int) -> bool:
        """检查用户权限"""
        return user_id in self.allowed_users
    
    async def permission_required(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """权限检查装饰器"""
        user_id = update.effective_user.id
        if not self.check_permission(user_id):
            await update.message.reply_text(
                "❌ 抱歉，您没有权限使用此机器人。\n"
                f"您的用户ID: `{user_id}`\n"
                "请联系管理员获取权限。",
                parse_mode=ParseMode.MARKDOWN
            )
            return False
        return True
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """开始命令"""
        if not await self.permission_required(update, context):
            return
        
        user = update.effective_user
        welcome_text = f"""
🚀 **CDN统计查询机器人**

欢迎 {user.first_name}！

这个机器人可以帮您查询和导出CDN统计数据。

**主要功能：**
• 📊 查询指定日期的数据
• 📁 批量导出CSV文件
• 🔧 测试数据库连接
• 📈 查看系统状态

使用 /help 查看详细命令说明。
        """
        
        keyboard = [
            [InlineKeyboardButton("📊 查询数据", callback_data="query_menu")],
            [InlineKeyboardButton("📁 批量导出", callback_data="batch_menu")],
            [InlineKeyboardButton("🔧 测试连接", callback_data="test_menu")],
            [InlineKeyboardButton("❓ 帮助", callback_data="help_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            welcome_text,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """帮助命令"""
        if not await self.permission_required(update, context):
            return
        
        help_text = """
📖 **CDN统计查询机器人 - 命令帮助**

**基本命令：**
• `/start` - 开始使用机器人
• `/help` - 显示此帮助信息
• `/status` - 查看系统状态

**查询命令：**
• `/query 2025-05-20` - 查询指定日期数据
• `/query 2025-05-20 2025-05-25` - 查询日期范围数据

**批量导出：**
• `/batch` - 从5月20日到昨天的所有数据
• `/batch 2025-05-20 2025-05-30` - 指定日期范围批量导出

**测试命令：**
• `/test` - 测试所有连接
• `/test ssh` - 测试SSH隧道
• `/test mongo` - 测试MongoDB连接
• `/test mysql` - 测试MySQL连接

**使用示例：**
```
/query 2025-05-20
/query 2025-05-20 2025-05-25
/batch
/batch 2025-05-20 2025-05-30
/test
```

**注意事项：**
• 日期格式：YYYY-MM-DD
• 大量数据导出可能需要较长时间
• CSV文件会直接发送到聊天窗口
        """
        
        await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)
    
    async def query_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询命令"""
        if not await self.permission_required(update, context):
            return
        
        args = context.args
        if not args:
            await update.message.reply_text(
                "❌ 请提供查询日期\n\n"
                "使用方法：\n"
                "`/query 2025-05-20` - 查询单天\n"
                "`/query 2025-05-20 2025-05-25` - 查询范围",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        try:
            start_date = args[0]
            end_date = args[1] if len(args) > 1 else None
            
            # 验证日期格式
            datetime.strptime(start_date, '%Y-%m-%d')
            if end_date:
                datetime.strptime(end_date, '%Y-%m-%d')
            
            await self.execute_query(update, start_date, end_date)
            
        except ValueError:
            await update.message.reply_text(
                "❌ 日期格式错误，请使用 YYYY-MM-DD 格式\n"
                "例如：2025-05-20"
            )
        except Exception as e:
            await update.message.reply_text(f"❌ 查询失败：{str(e)}")
    
    async def batch_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """批量导出命令"""
        if not await self.permission_required(update, context):
            return
        
        args = context.args
        
        # 默认从5月20日到昨天
        start_date = "2025-05-20"
        end_date = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        if len(args) >= 1:
            start_date = args[0]
        if len(args) >= 2:
            end_date = args[1]
        
        try:
            # 验证日期格式
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
            
            await self.execute_batch_export(update, start_date, end_date)
            
        except ValueError:
            await update.message.reply_text(
                "❌ 日期格式错误，请使用 YYYY-MM-DD 格式\n"
                "例如：/batch 2025-05-20 2025-05-30"
            )
        except Exception as e:
            await update.message.reply_text(f"❌ 批量导出失败：{str(e)}")
    
    async def test_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """测试命令"""
        if not await self.permission_required(update, context):
            return
        
        args = context.args
        test_type = args[0] if args else "all"
        
        await self.execute_test(update, test_type)
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """状态命令"""
        if not await self.permission_required(update, context):
            return
        
        status_text = f"""
📊 **系统状态**

🕐 **当前时间：** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 **配置状态：**
• SSH隧道：{'✅ 启用' if Config.USE_SSH_TUNNEL else '❌ 禁用'}
• MySQL查询：{'✅ 启用' if Config.ENABLE_USER_EMAIL_LOOKUP else '❌ 禁用'}

👥 **授权用户：** {len(self.allowed_users)} 人
🤖 **机器人版本：** v1.0
        """
        
        await update.message.reply_text(status_text, parse_mode=ParseMode.MARKDOWN)
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """按钮回调处理"""
        query = update.callback_query
        await query.answer()
        
        if not self.check_permission(query.from_user.id):
            await query.edit_message_text("❌ 您没有权限使用此功能")
            return
        
        data = query.data
        
        if data == "query_menu":
            await self.show_query_menu(query)
        elif data == "batch_menu":
            await self.show_batch_menu(query)
        elif data == "test_menu":
            await self.show_test_menu(query)
        elif data == "help_menu":
            await self.show_help_menu(query)
        elif data.startswith("quick_query_"):
            date_str = data.replace("quick_query_", "")
            await self.execute_query_from_callback(query, date_str)
        elif data.startswith("test_"):
            test_type = data.replace("test_", "")
            await self.execute_test_from_callback(query, test_type)
    
    async def show_query_menu(self, query):
        """显示查询菜单"""
        today = date.today()
        yesterday = today - timedelta(days=1)
        week_ago = today - timedelta(days=7)
        
        keyboard = [
            [InlineKeyboardButton(f"📊 昨天 ({yesterday})", callback_data=f"quick_query_{yesterday}")],
            [InlineKeyboardButton(f"📊 一周前 ({week_ago})", callback_data=f"quick_query_{week_ago}")],
            [InlineKeyboardButton("📊 5月20日", callback_data="quick_query_2025-05-20")],
            [InlineKeyboardButton("🔙 返回主菜单", callback_data="main_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = """
📊 **快速查询菜单**

选择要查询的日期，或使用命令：
`/query 2025-05-20` - 查询指定日期
`/query 2025-05-20 2025-05-25` - 查询日期范围
        """
        
        await query.edit_message_text(text, parse_mode=ParseMode.MARKDOWN, reply_markup=reply_markup)
    
    async def show_batch_menu(self, query):
        """显示批量导出菜单"""
        keyboard = [
            [InlineKeyboardButton("📁 从5月20日到昨天", callback_data="batch_default")],
            [InlineKeyboardButton("📁 最近7天", callback_data="batch_week")],
            [InlineKeyboardButton("📁 最近30天", callback_data="batch_month")],
            [InlineKeyboardButton("🔙 返回主菜单", callback_data="main_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = """
📁 **批量导出菜单**

选择导出范围，或使用命令：
`/batch` - 从5月20日到昨天
`/batch 2025-05-20 2025-05-30` - 指定日期范围

⚠️ 注意：大量数据导出可能需要较长时间
        """
        
        await query.edit_message_text(text, parse_mode=ParseMode.MARKDOWN, reply_markup=reply_markup)
    
    async def show_test_menu(self, query):
        """显示测试菜单"""
        keyboard = [
            [InlineKeyboardButton("🔧 测试所有连接", callback_data="test_all")],
            [InlineKeyboardButton("🔐 测试SSH隧道", callback_data="test_ssh")],
            [InlineKeyboardButton("🍃 测试MongoDB", callback_data="test_mongo")],
            [InlineKeyboardButton("🐬 测试MySQL", callback_data="test_mysql")],
            [InlineKeyboardButton("🔙 返回主菜单", callback_data="main_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = """
🔧 **连接测试菜单**

选择要测试的连接类型：
        """
        
        await query.edit_message_text(text, parse_mode=ParseMode.MARKDOWN, reply_markup=reply_markup)
    
    async def execute_query(self, update: Update, start_date: str, end_date: str = None):
        """执行查询"""
        # 发送处理中消息
        processing_msg = await update.message.reply_text("🔄 正在查询数据，请稍候...")
        
        try:
            # 创建查询工具
            tool = CDNQueryTool()
            
            # 执行查询
            results = tool.query_flux_by_date_range(
                start_date=start_date,
                end_date=end_date,
                limit=100  # 限制返回数量
            )
            
            if results:
                # 格式化结果
                formatted_results = tool.format_results(results, show_email=True)
                
                # 创建临时CSV文件
                temp_dir = tempfile.mkdtemp()
                csv_filename = f"cdn_stats_{start_date}"
                if end_date:
                    csv_filename += f"_to_{end_date}"
                csv_filename += ".csv"
                csv_path = os.path.join(temp_dir, csv_filename)
                
                # 导出CSV
                tool.export_to_csv(formatted_results, csv_path)
                
                # 发送结果摘要
                summary_text = f"""
✅ **查询完成**

📅 **查询日期：** {start_date}
{f'📅 **结束日期：** {end_date}' if end_date else ''}
📊 **记录数量：** {len(formatted_results)}
📁 **文件名：** {csv_filename}

前5条记录预览：
"""
                
                # 添加前5条记录预览
                for i, record in enumerate(formatted_results[:5], 1):
                    summary_text += f"\n{i}. 用户ID: {record['用户ID']}"
                    if '用户邮箱' in record:
                        summary_text += f" | 邮箱: {record['用户邮箱']}"
                    summary_text += f" | 流量: {record['总流量(可读)']}"
                
                if len(formatted_results) > 5:
                    summary_text += f"\n... 还有 {len(formatted_results) - 5} 条记录"
                
                await processing_msg.edit_text(summary_text, parse_mode=ParseMode.MARKDOWN)
                
                # 发送CSV文件
                with open(csv_path, 'rb') as csv_file:
                    await update.message.reply_document(
                        document=csv_file,
                        filename=csv_filename,
                        caption=f"📁 CDN统计数据 - {start_date}" + (f" 到 {end_date}" if end_date else "")
                    )
                
                # 清理临时文件
                shutil.rmtree(temp_dir)
                
            else:
                await processing_msg.edit_text(
                    f"❌ 未找到数据\n\n"
                    f"查询日期：{start_date}" + (f" 到 {end_date}" if end_date else "")
                )
            
            tool.close_connection()
            
        except Exception as e:
            await processing_msg.edit_text(f"❌ 查询失败：{str(e)}")
            logger.error(f"Query failed: {e}")
    
    async def execute_batch_export(self, update: Update, start_date: str, end_date: str):
        """执行批量导出"""
        # 发送处理中消息
        processing_msg = await update.message.reply_text(
            f"🔄 正在批量导出数据...\n\n"
            f"📅 日期范围：{start_date} 到 {end_date}\n"
            f"⏳ 这可能需要几分钟时间，请耐心等待..."
        )
        
        try:
            # 计算天数
            start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
            total_days = (end_dt - start_dt).days + 1
            
            if total_days > 30:
                await processing_msg.edit_text(
                    "❌ 日期范围过大（超过30天），请缩小范围后重试"
                )
                return
            
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            
            # 创建查询工具
            tool = CDNQueryTool()
            
            success_count = 0
            total_records = 0
            current_date = start_dt
            
            while current_date <= end_dt:
                date_str = current_date.strftime('%Y-%m-%d')
                
                try:
                    # 查询单天数据
                    results = tool.query_flux_by_date_range(start_date=date_str)
                    
                    if results:
                        # 格式化结果
                        formatted_results = tool.format_results(results, show_email=True)
                        
                        # 导出CSV
                        csv_filename = f"cdn_stats_{date_str}.csv"
                        csv_path = os.path.join(temp_dir, csv_filename)
                        tool.export_to_csv(formatted_results, csv_path)
                        
                        success_count += 1
                        total_records += len(formatted_results)
                    
                    # 更新进度
                    progress = ((current_date - start_dt).days + 1) / total_days * 100
                    await processing_msg.edit_text(
                        f"🔄 正在导出数据... {progress:.1f}%\n\n"
                        f"📅 当前处理：{date_str}\n"
                        f"✅ 已完成：{success_count} 天\n"
                        f"📊 总记录数：{total_records}"
                    )
                    
                except Exception as e:
                    logger.error(f"Failed to export {date_str}: {e}")
                
                current_date += timedelta(days=1)
                
                # 短暂延迟，避免数据库压力
                await asyncio.sleep(0.5)
            
            tool.close_connection()
            
            # 创建压缩包
            import zipfile
            zip_filename = f"cdn_batch_export_{start_date}_to_{end_date}.zip"
            zip_path = os.path.join(temp_dir, zip_filename)
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if file.endswith('.csv'):
                            file_path = os.path.join(root, file)
                            zipf.write(file_path, file)
            
            # 发送结果
            summary_text = f"""
✅ **批量导出完成**

📅 **日期范围：** {start_date} 到 {end_date}
📊 **总天数：** {total_days}
✅ **成功导出：** {success_count} 天
📊 **总记录数：** {total_records}
📁 **文件格式：** ZIP压缩包
            """
            
            await processing_msg.edit_text(summary_text, parse_mode=ParseMode.MARKDOWN)
            
            # 发送ZIP文件
            with open(zip_path, 'rb') as zip_file:
                await update.message.reply_document(
                    document=zip_file,
                    filename=zip_filename,
                    caption=f"📦 CDN批量导出数据 - {start_date} 到 {end_date}"
                )
            
            # 清理临时文件
            shutil.rmtree(temp_dir)
            
        except Exception as e:
            await processing_msg.edit_text(f"❌ 批量导出失败：{str(e)}")
            logger.error(f"Batch export failed: {e}")
    
    async def execute_test(self, update: Update, test_type: str):
        """执行连接测试"""
        processing_msg = await update.message.reply_text("🔄 正在测试连接...")
        
        try:
            test_results = []
            
            if test_type in ["all", "ssh"]:
                # 测试SSH隧道
                try:
                    from ssh_tunnel_manager import SSHTunnelManager
                    tunnel_manager = SSHTunnelManager()
                    if tunnel_manager.create_tunnel():
                        test_results.append("✅ SSH隧道连接成功")
                        tunnel_manager.close_tunnel()
                    else:
                        test_results.append("❌ SSH隧道连接失败")
                except Exception as e:
                    test_results.append(f"❌ SSH隧道测试异常：{str(e)}")
            
            if test_type in ["all", "mongo"]:
                # 测试MongoDB
                try:
                    tool = CDNQueryTool()
                    # 简单测试查询
                    yesterday = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
                    results = tool.query_flux_by_date_range(start_date=yesterday, limit=1)
                    test_results.append("✅ MongoDB连接成功")
                    tool.close_connection()
                except Exception as e:
                    test_results.append(f"❌ MongoDB连接失败：{str(e)}")
            
            if test_type in ["all", "mysql"]:
                # 测试MySQL
                try:
                    from mysql_manager import MySQLManager
                    mysql_manager = MySQLManager()
                    if mysql_manager.test_connection():
                        test_results.append("✅ MySQL连接成功")
                    else:
                        test_results.append("❌ MySQL连接失败")
                    mysql_manager.close_connection()
                except Exception as e:
                    test_results.append(f"❌ MySQL连接失败：{str(e)}")
            
            result_text = f"🔧 **连接测试结果**\n\n" + "\n".join(test_results)
            await processing_msg.edit_text(result_text, parse_mode=ParseMode.MARKDOWN)
            
        except Exception as e:
            await processing_msg.edit_text(f"❌ 测试失败：{str(e)}")
    
    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE):
        """错误处理器"""
        logger.error(f"Exception while handling an update: {context.error}")
        
        if isinstance(update, Update) and update.message:
            await update.message.reply_text(
                "❌ 发生了一个错误，请稍后重试或联系管理员。"
            )
    
    async def setup_commands(self):
        """设置机器人命令菜单"""
        commands = [
            BotCommand("start", "开始使用机器人"),
            BotCommand("help", "查看帮助信息"),
            BotCommand("query", "查询指定日期数据"),
            BotCommand("batch", "批量导出数据"),
            BotCommand("test", "测试数据库连接"),
            BotCommand("status", "查看系统状态"),
        ]
        
        await self.application.bot.set_my_commands(commands)
    
    async def run(self):
        """运行机器人"""
        # 设置命令菜单
        await self.setup_commands()
        
        # 启动机器人
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()
        
        logger.info("CDN Telegram Bot started successfully!")
        
        # 保持运行
        await self.application.updater.idle()
        
        # 清理
        await self.application.updater.stop()
        await self.application.stop()
        await self.application.shutdown()


def main():
    """主函数"""
    # 从环境变量获取配置
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not bot_token:
        print("❌ 请设置环境变量 TELEGRAM_BOT_TOKEN")
        sys.exit(1)
    
    # 从环境变量获取允许的用户ID
    allowed_users_str = os.getenv('TELEGRAM_ALLOWED_USERS', '')
    if not allowed_users_str:
        print("❌ 请设置环境变量 TELEGRAM_ALLOWED_USERS")
        print("格式：用户ID1,用户ID2,用户ID3")
        sys.exit(1)
    
    try:
        allowed_users = [int(uid.strip()) for uid in allowed_users_str.split(',')]
    except ValueError:
        print("❌ TELEGRAM_ALLOWED_USERS 格式错误，请使用逗号分隔的数字")
        sys.exit(1)
    
    print(f"🤖 启动CDN Telegram机器人...")
    print(f"📋 允许的用户数量: {len(allowed_users)}")
    
    # 创建并运行机器人
    bot = CDNTelegramBot(bot_token, allowed_users)
    
    try:
        asyncio.run(bot.run())
    except KeyboardInterrupt:
        print("\n🛑 机器人已停止")
    except Exception as e:
        print(f"❌ 机器人运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
