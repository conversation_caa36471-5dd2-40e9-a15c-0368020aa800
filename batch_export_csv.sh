#!/bin/bash

# CDN统计数据批量导出脚本
# 从2025-05-20开始到昨天，按日期生成CSV文件

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python脚本是否存在
check_requirements() {
    if [ ! -f "main.py" ]; then
        print_error "main.py 文件不存在，请确保在正确的目录中运行此脚本"
        exit 1
    fi
    
    if [ ! -f ".env" ]; then
        print_error ".env 配置文件不存在，请先配置数据库连接"
        exit 1
    fi
    
    print_info "环境检查通过"
}

# 获取昨天的日期
get_yesterday() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        date -v-1d +%Y-%m-%d
    else
        # Linux
        date -d "yesterday" +%Y-%m-%d
    fi
}

# 将日期转换为时间戳（用于比较）
date_to_timestamp() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        date -j -f "%Y-%m-%d" "$1" +%s
    else
        # Linux
        date -d "$1" +%s
    fi
}

# 获取下一天日期
get_next_date() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        date -j -v+1d -f "%Y-%m-%d" "$1" +%Y-%m-%d
    else
        # Linux
        date -d "$1 + 1 day" +%Y-%m-%d
    fi
}

# 导出单日数据
export_single_day() {
    local date=$1
    local output_dir=$2
    local csv_file="${output_dir}/cdn_stats_${date}.csv"
    
    print_info "正在导出 ${date} 的数据..."
    
    # 执行查询并导出CSV
    python main.py query -s "$date" -o "$csv_file"
    
    if [ $? -eq 0 ] && [ -f "$csv_file" ]; then
        # 检查文件大小
        local file_size=$(wc -l < "$csv_file" 2>/dev/null || echo "0")
        if [ "$file_size" -gt 1 ]; then
            print_success "✅ ${date}: 导出成功，共 $((file_size-1)) 条记录"
            return 0
        else
            print_warning "⚠️  ${date}: 文件已生成但无数据记录"
            return 1
        fi
    else
        print_error "❌ ${date}: 导出失败"
        return 1
    fi
}

# 生成汇总报告
generate_summary() {
    local output_dir=$1
    local summary_file="${output_dir}/export_summary.txt"
    
    print_info "生成汇总报告..."
    
    {
        echo "CDN统计数据导出汇总报告"
        echo "=========================="
        echo "导出时间: $(date)"
        echo "导出目录: $output_dir"
        echo ""
        echo "文件列表:"
        echo "--------"
        
        local total_files=0
        local total_records=0
        
        for csv_file in "$output_dir"/cdn_stats_*.csv; do
            if [ -f "$csv_file" ]; then
                local filename=$(basename "$csv_file")
                local record_count=$(($(wc -l < "$csv_file") - 1))
                echo "$filename: $record_count 条记录"
                total_files=$((total_files + 1))
                total_records=$((total_records + record_count))
            fi
        done
        
        echo ""
        echo "统计汇总:"
        echo "--------"
        echo "总文件数: $total_files"
        echo "总记录数: $total_records"
        
    } > "$summary_file"
    
    print_success "汇总报告已生成: $summary_file"
}

# 主函数
main() {
    print_info "CDN统计数据批量导出工具"
    echo "==============================="
    
    # 检查环境
    check_requirements
    
    # 设置日期范围
    local start_date="2025-05-20"
    local end_date=$(get_yesterday)
    
    print_info "导出日期范围: $start_date 到 $end_date"
    
    # 创建输出目录
    local output_dir="exports/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$output_dir"
    print_info "输出目录: $output_dir"
    
    # 验证日期范围
    local start_ts=$(date_to_timestamp "$start_date")
    local end_ts=$(date_to_timestamp "$end_date")
    
    if [ "$start_ts" -gt "$end_ts" ]; then
        print_error "开始日期不能晚于结束日期"
        exit 1
    fi
    
    # 计算总天数
    local total_days=$(( (end_ts - start_ts) / 86400 + 1 ))
    print_info "总共需要导出 $total_days 天的数据"
    
    # 循环导出每一天的数据
    local current_date="$start_date"
    local success_count=0
    local failed_count=0
    
    while [ "$(date_to_timestamp "$current_date")" -le "$end_ts" ]; do
        if export_single_day "$current_date" "$output_dir"; then
            success_count=$((success_count + 1))
        else
            failed_count=$((failed_count + 1))
        fi
        
        # 获取下一天
        current_date=$(get_next_date "$current_date")
        
        # 添加短暂延迟，避免对数据库造成过大压力
        sleep 1
    done
    
    echo ""
    print_info "导出完成统计:"
    print_success "成功: $success_count 天"
    if [ "$failed_count" -gt 0 ]; then
        print_warning "失败: $failed_count 天"
    fi
    
    # 生成汇总报告
    generate_summary "$output_dir"
    
    echo ""
    print_success "🎉 批量导出完成！"
    print_info "输出目录: $output_dir"
    print_info "查看汇总: cat $output_dir/export_summary.txt"
}

# 显示帮助信息
show_help() {
    echo "CDN统计数据批量导出工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -s, --start    指定开始日期 (格式: YYYY-MM-DD, 默认: 2025-05-20)"
    echo "  -e, --end      指定结束日期 (格式: YYYY-MM-DD, 默认: 昨天)"
    echo "  -o, --output   指定输出目录 (默认: exports/时间戳)"
    echo ""
    echo "示例:"
    echo "  $0                           # 导出从2025-05-20到昨天的数据"
    echo "  $0 -s 2025-05-25 -e 2025-05-30  # 导出指定日期范围的数据"
    echo "  $0 -o /path/to/output        # 指定输出目录"
}

# 解析命令行参数
parse_args() {
    local start_date="2025-05-20"
    local end_date=$(get_yesterday)
    local output_dir=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--start)
                start_date="$2"
                shift 2
                ;;
            -e|--end)
                end_date="$2"
                shift 2
                ;;
            -o|--output)
                output_dir="$2"
                shift 2
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定输出目录，使用默认值
    if [ -z "$output_dir" ]; then
        output_dir="exports/$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 导出变量供main函数使用
    export BATCH_START_DATE="$start_date"
    export BATCH_END_DATE="$end_date"
    export BATCH_OUTPUT_DIR="$output_dir"
}

# 带参数的主函数
main_with_args() {
    print_info "CDN统计数据批量导出工具"
    echo "==============================="
    
    # 检查环境
    check_requirements
    
    # 使用解析的参数
    local start_date="$BATCH_START_DATE"
    local end_date="$BATCH_END_DATE"
    local output_dir="$BATCH_OUTPUT_DIR"
    
    print_info "导出日期范围: $start_date 到 $end_date"
    
    # 创建输出目录
    mkdir -p "$output_dir"
    print_info "输出目录: $output_dir"
    
    # 验证日期范围
    local start_ts=$(date_to_timestamp "$start_date")
    local end_ts=$(date_to_timestamp "$end_date")
    
    if [ "$start_ts" -gt "$end_ts" ]; then
        print_error "开始日期不能晚于结束日期"
        exit 1
    fi
    
    # 计算总天数
    local total_days=$(( (end_ts - start_ts) / 86400 + 1 ))
    print_info "总共需要导出 $total_days 天的数据"
    
    # 循环导出每一天的数据
    local current_date="$start_date"
    local success_count=0
    local failed_count=0
    
    while [ "$(date_to_timestamp "$current_date")" -le "$end_ts" ]; do
        if export_single_day "$current_date" "$output_dir"; then
            success_count=$((success_count + 1))
        else
            failed_count=$((failed_count + 1))
        fi
        
        # 获取下一天
        current_date=$(get_next_date "$current_date")
        
        # 添加短暂延迟，避免对数据库造成过大压力
        sleep 1
    done
    
    echo ""
    print_info "导出完成统计:"
    print_success "成功: $success_count 天"
    if [ "$failed_count" -gt 0 ]; then
        print_warning "失败: $failed_count 天"
    fi
    
    # 生成汇总报告
    generate_summary "$output_dir"
    
    echo ""
    print_success "🎉 批量导出完成！"
    print_info "输出目录: $output_dir"
    print_info "查看汇总: cat $output_dir/export_summary.txt"
}

# 脚本入口点
if [ $# -eq 0 ]; then
    # 无参数时使用默认设置
    main
else
    # 有参数时解析参数
    parse_args "$@"
    main_with_args
fi
