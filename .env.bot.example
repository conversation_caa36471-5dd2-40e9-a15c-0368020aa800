
# Telegram机器人配置模板
# 复制此文件为 .env.bot 并填入实际值

# ===========================================
# Telegram Bot 基本配置
# ===========================================

# Bot Token (从 @BotFather 获取)
TELEGRAM_BOT_TOKEN=your_bot_token_here

# 允许使用机器人的用户ID (逗号分隔)
# 获取用户ID: 发送消息给 @userinfobot
TELEGRAM_ALLOWED_USERS=123456789,987654321

# 管理员用户ID (可选，逗号分隔)
TELEGRAM_ADMIN_USERS=123456789

# ===========================================
# 机器人功能限制
# ===========================================

# 最大查询天数
BOT_MAX_QUERY_DAYS=30

# 最大批量导出天数
BOT_MAX_BATCH_DAYS=30

# 单次查询最大记录数
BOT_MAX_RECORDS_PER_QUERY=1000

# 最大文件大小(MB)
BOT_MAX_FILE_SIZE_MB=50

# 临时文件目录
BOT_TEMP_DIR=/tmp/cdn_bot

# 每用户每分钟最大请求数
BOT_RATE_LIMIT_PER_USER=10

# ===========================================
# 数据库配置 (继承自主配置文件)
# ===========================================
# 机器人会自动读取现有的数据库配置
# 确保 .env 文件中的数据库配置正确

# ===========================================
# 使用说明
# ===========================================
# 1. 创建Telegram机器人:
#    - 发送 /newbot 给 @BotFather
#    - 按提示设置机器人名称和用户名
#    - 获取Bot Token并填入上方
#
# 2. 获取用户ID:
#    - 发送任意消息给 @userinfobot
#    - 获取您的用户ID并填入上方
#
# 3. 启动机器人:
#    python telegram_bot.py
#
# 4. 测试机器人:
#    - 在Telegram中搜索您的机器人
#    - 发送 /start 开始使用
