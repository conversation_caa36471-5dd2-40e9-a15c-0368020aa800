#!/usr/bin/env python3
"""
CDN统计数据查询工具
基于MongoDB聚合查询的命令行工具
"""

import click
from datetime import datetime
from tabulate import tabulate
from cdn_query_tool import CDNQueryTool


@click.command()
@click.option('--start-date', '-s', required=True, 
              help='开始日期，格式：2025-05-20 或 2025-05-20 00:00:00')
@click.option('--end-date', '-e', default=None,
              help='结束日期，格式同上。不提供则查询单天数据')
@click.option('--metric-type', '-m', default='access',
              help='指标类型，默认为 access')
@click.option('--limit', '-l', type=int, default=None,
              help='限制返回结果数量')
@click.option('--export', '-o', default=None,
              help='导出结果到CSV文件，指定文件名')
@click.option('--format', '-f', type=click.Choice(['table', 'json']), default='table',
              help='输出格式：table 或 json')
def query_cdn_stats(start_date, end_date, metric_type, limit, export, format):
    """查询CDN统计数据的命令行工具"""
    
    tool = None
    try:
        # 创建查询工具实例
        tool = CDNQueryTool()
        
        # 执行查询
        click.echo(f"正在查询 {metric_type} 类型的统计数据...")
        results = tool.query_flux_by_date_range(
            start_date=start_date,
            end_date=end_date,
            metric_type=metric_type,
            limit=limit
        )
        
        if not results:
            click.echo("没有找到匹配的数据")
            return
        
        # 格式化结果
        formatted_results = tool.format_results(results)
        
        # 显示结果
        if format == 'table':
            click.echo("\n查询结果:")
            click.echo(tabulate(formatted_results, headers='keys', tablefmt='grid'))
        elif format == 'json':
            import json
            click.echo(json.dumps(formatted_results, ensure_ascii=False, indent=2))
        
        # 导出到CSV
        if export:
            tool.export_to_csv(formatted_results, export)
        
        # 显示统计信息
        total_users = len(results)
        total_flux = sum(result['total_flux'] for result in results)
        click.echo(f"\n统计信息:")
        click.echo(f"用户总数: {total_users}")
        click.echo(f"总流量: {tool._format_bytes(total_flux)}")
        
    except Exception as e:
        click.echo(f"执行失败: {e}", err=True)
    finally:
        if tool:
            tool.close_connection()


@click.group()
def cli():
    """CDN统计数据查询工具"""
    pass


@cli.command()
def test_connection():
    """测试数据库连接"""
    try:
        tool = CDNQueryTool()
        click.echo("数据库连接测试成功！")
        tool.close_connection()
    except Exception as e:
        click.echo(f"数据库连接失败: {e}", err=True)


@cli.command()
@click.option('--date', '-d', default=None,
              help='查询指定日期的数据，格式：2025-05-20')
def quick_query(date):
    """快速查询指定日期的数据"""
    if date is None:
        # 默认查询昨天的数据
        from datetime import datetime, timedelta
        yesterday = datetime.now() - timedelta(days=1)
        date = yesterday.strftime('%Y-%m-%d')
    
    click.echo(f"快速查询 {date} 的数据...")
    
    tool = None
    try:
        tool = CDNQueryTool()
        results = tool.query_flux_by_date_range(start_date=date, limit=10)
        
        if results:
            formatted_results = tool.format_results(results)
            click.echo(tabulate(formatted_results, headers='keys', tablefmt='simple'))
        else:
            click.echo("没有找到数据")
            
    except Exception as e:
        click.echo(f"查询失败: {e}", err=True)
    finally:
        if tool:
            tool.close_connection()


# 添加命令到CLI组
cli.add_command(query_cdn_stats, name='query')

if __name__ == '__main__':
    cli()
