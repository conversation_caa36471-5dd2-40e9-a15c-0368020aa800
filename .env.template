# ===========================================
# CDN统计查询工具配置文件
# ===========================================

# MongoDB 配置（直接连接模式）
# 请将 YOUR_MONGODB_PASSWORD 替换为实际的MongoDB密码
MONGODB_URI=*******************************************************************************
DATABASE_NAME=your_database_name
COLLECTION_NAME=cdn_statistics_total_metric

# SSH 隧道配置（当前使用直连，无需SSH隧道）
USE_SSH_TUNNEL=false
SSH_HOST=
SSH_PORT=22
SSH_USERNAME=
SSH_PASSWORD=
SSH_KEY_FILE=

# 远程MongoDB配置（仅在SSH隧道模式下使用）
REMOTE_MONGODB_HOST=localhost
REMOTE_MONGODB_PORT=27017
LOCAL_TUNNEL_PORT=27018

# MySQL 配置（用于查询用户邮箱信息）
# 设置为 true 启用邮箱查询功能
ENABLE_USER_EMAIL_LOOKUP=true
MYSQL_HOST=*************
MYSQL_PORT=3306
MYSQL_USERNAME=your_mysql_username
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=your_mysql_database
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai

# ===========================================
# 配置说明：
# 1. 将 YOUR_MONGODB_PASSWORD 替换为实际的MongoDB密码
# 2. 设置正确的数据库名称和集合名称
# 3. 如需查询用户邮箱，配置MySQL连接信息
# 4. 保存后重命名为 .env 文件
# ===========================================
