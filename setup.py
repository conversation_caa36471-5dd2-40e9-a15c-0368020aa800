#!/usr/bin/env python3
"""
CDN统计查询工具安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    with open('README.md', 'r', encoding='utf-8') as f:
        return f.read()

# 读取requirements
def read_requirements():
    with open('requirements.txt', 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="cdn-stats-tool",
    version="1.0.0",
    description="CDN统计数据查询和导出工具",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="Your Name",
    author_email="<EMAIL>",
    url="https://github.com/yourusername/cdn-stats-tool",
    packages=find_packages(),
    py_modules=[
        'main',
        'cdn_query_tool',
        'mysql_manager',
        'ssh_tunnel_manager',
        'config',
        'batch_export',
        'setup_wizard',
        'configure_ssh_tunnel',
        'test_ssh_connection',
        'test_mysql_integration',
        'debug_user_id',
        'quick_test_email',
        'test_dual_tunnel'
    ],
    install_requires=read_requirements(),
    entry_points={
        'console_scripts': [
            'cdn-query=main:cli',
            'cdn-setup=setup_wizard:cli',
            'cdn-ssh-setup=configure_ssh_tunnel:cli',
            'cdn-batch-export=batch_export:batch_export',
        ],
    },
    include_package_data=True,
    package_data={
        '': ['*.md', '*.txt', '*.sh', '.env.example', '.env.ssh_template'],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    zip_safe=False,
)
