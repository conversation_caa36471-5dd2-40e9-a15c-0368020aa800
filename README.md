# CDN统计查询工具

一个用于查询和导出CDN统计数据的工具，支持命令行和Telegram机器人两种使用方式。

## 🚀 功能特性

- 📊 **数据查询**: 按日期查询CDN统计数据
- 📁 **批量导出**: 批量导出多天数据到CSV文件
- 🔐 **SSH隧道**: 通过SSH隧道连接远程数据库
- 👤 **用户关联**: 自动关联用户ID和邮箱信息
- 🤖 **Telegram机器人**: 支持通过Telegram操作
- 📱 **移动友好**: 随时随地查询数据

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

### 方式1: 使用配置向导（推荐）

```bash
# 运行配置向导
python setup_wizard.py setup

# 验证配置
python setup_wizard.py validate

# 查看当前配置
python setup_wizard.py show
```

### 方式2: 手动配置

1. 复制环境变量配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置你的MongoDB连接信息：

### 直接连接MongoDB（不使用SSH隧道）
```env
USE_SSH_TUNNEL=false
MONGODB_URI=mongodb://your-host:27017/
DATABASE_NAME=your_database_name
COLLECTION_NAME=cdn_statistics_total_metric
DEFAULT_TIMEZONE=Asia/Shanghai
```

### 通过SSH隧道连接MongoDB（推荐用于远程服务器）
```env
# 启用SSH隧道
USE_SSH_TUNNEL=true

# SSH服务器配置
SSH_HOST=your-ssh-server.com
SSH_PORT=22
SSH_USERNAME=your_username

# SSH认证方式1：使用密码（不推荐）
SSH_PASSWORD=your_password

# SSH认证方式2：使用密钥文件（推荐）
SSH_KEY_FILE=/path/to/your/private_key

# 远程MongoDB配置
REMOTE_MONGODB_HOST=localhost
REMOTE_MONGODB_PORT=27017

# 本地隧道端口
LOCAL_TUNNEL_PORT=27018

# 数据库配置
DATABASE_NAME=your_database_name
COLLECTION_NAME=cdn_statistics_total_metric

# MySQL配置（用于查询用户邮箱）
ENABLE_USER_EMAIL_LOOKUP=true
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USERNAME=your_mysql_username
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=your_mysql_database
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email

DEFAULT_TIMEZONE=Asia/Shanghai
```

## 使用方法

### 1. 测试连接

```bash
# 测试SSH隧道连接（如果启用）
python main.py test-ssh

# 测试MySQL连接（如果启用）
python main.py test-mysql

# 测试MongoDB连接
python main.py test-connection
```

### 2. 查询指定日期的数据

```bash
# 查询单天数据（2025-05-20）
python main.py query -s "2025-05-20"

# 查询指定时间范围
python main.py query -s "2025-05-20" -e "2025-05-21"

# 查询并限制返回前10条记录
python main.py query -s "2025-05-20" -l 10

# 导出结果到CSV文件
python main.py query -s "2025-05-20" -o "result.csv"

# 以JSON格式输出
python main.py query -s "2025-05-20" -f json

# 不显示用户邮箱信息
python main.py query -s "2025-05-20" --no-email
```

### 3. 快速查询

```bash
# 快速查询指定日期（显示前10条）
python main.py quick-query -d "2025-05-20"

# 快速查询昨天的数据
python main.py quick-query
```

## 命令参数说明

### query 命令

- `-s, --start-date`: 开始日期（必需），格式：`2025-05-20` 或 `2025-05-20 00:00:00`
- `-e, --end-date`: 结束日期（可选），格式同上
- `-m, --metric-type`: 指标类型，默认为 `access`
- `-l, --limit`: 限制返回结果数量
- `-o, --export`: 导出结果到CSV文件
- `-f, --format`: 输出格式，可选 `table` 或 `json`
- `--no-email`: 不显示用户邮箱信息

## 示例输出

```
查询时间范围 (UTC): 2025-05-19 16:00:00 到 2025-05-20 16:00:00
正在查询 3 个用户的email信息...
成功获取 3 个用户的email信息
查询完成，共找到 3 条记录

查询结果:
+------+----------------+----------------+------------------------+----------------+
| 排名 | 用户ID         | 总流量(字节)   | 用户邮箱               | 总流量(可读)   |
+======+================+================+========================+================+
| 1    | 2796484965376  | 1073741824     | <EMAIL>       | 1.00 GB        |
+------+----------------+----------------+------------------------+----------------+
| 2    | 2796484965377  | 536870912      | <EMAIL>      | 512.00 MB      |
+------+----------------+----------------+------------------------+----------------+
| 3    | 2796484965378  | 268435456      | <EMAIL>       | 256.00 MB      |
+------+----------------+----------------+------------------------+----------------+

统计信息:
用户总数: 3
总流量: 1.75 GB
```

## 原始MongoDB查询

工具基于以下MongoDB聚合查询：

```javascript
db.getCollection("cdn_statistics_total_metric").aggregate([
  {
    $match: {
      metric_type: "access",
      statistics_time: {
        $gte: new Date("2025-05-19T16:00:00Z"),
        $lt: new Date("2025-05-20T16:00:00Z")
      }
    }
  },
  {
    $group: {
      _id: "$user_id",
      total_flux: { $sum: "$flux" }
    }
  },
  {
    $sort: { total_flux: -1 }
  }
]);
```

## SSH隧道配置说明

### 1. 生成SSH密钥（推荐）

```bash
# 生成SSH密钥对
ssh-keygen -t rsa -b 4096 -f ~/.ssh/mongodb_key

# 将公钥复制到远程服务器
ssh-copy-id -i ~/.ssh/mongodb_key.pub <EMAIL>
```

### 2. 配置SSH隧道

在 `.env` 文件中设置：
```env
USE_SSH_TUNNEL=true
SSH_HOST=your-server.com
SSH_USERNAME=your_username
SSH_KEY_FILE=/Users/<USER>/.ssh/mongodb_key
REMOTE_MONGODB_HOST=localhost
REMOTE_MONGODB_PORT=27017
LOCAL_TUNNEL_PORT=27018
```

### 3. 测试SSH连接

```bash
# 测试SSH连接
ssh -i ~/.ssh/mongodb_key <EMAIL>

# 测试端口转发
ssh -i ~/.ssh/mongodb_key -L 27018:localhost:27017 <EMAIL>
```

## MySQL用户邮箱查询配置

### 1. 启用邮箱查询功能

在 `.env` 文件中设置：
```env
ENABLE_USER_EMAIL_LOOKUP=true
MYSQL_HOST=your-mysql-server.com
MYSQL_PORT=3306
MYSQL_USERNAME=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email
```

### 2. 测试MySQL连接

```bash
# 测试MySQL连接和用户查询
python main.py test-mysql

# 运行完整的MySQL集成测试
python test_mysql_integration.py
```

### 3. 用户表结构要求

确保MySQL用户表包含以下字段：
- 用户ID字段（默认：`id`）
- 用户邮箱字段（默认：`email`）

示例SQL：
```sql
SELECT id, email FROM vc_user WHERE id = "2796484965376";
```

## 注意事项

- 时间输入使用北京时间，工具会自动转换为UTC时间进行查询
- 确保MongoDB服务正在运行且可访问
- 使用SSH隧道时，确保SSH服务器可以访问MongoDB
- 大量数据查询时建议使用 `--limit` 参数限制结果数量
- SSH密钥文件权限应设置为600：`chmod 600 ~/.ssh/mongodb_key`
- 本地隧道端口不要与现有服务冲突
