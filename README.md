# CDN统计查询工具

基于Telegram机器人的CDN统计数据查询工具，支持随时随地查询和导出数据。

## 🚀 功能特性

- 🤖 **Telegram机器人**: 主要使用方式，移动端友好
- 📊 **数据查询**: 按日期查询CDN统计数据
- 📁 **批量导出**: 批量导出多天数据到CSV文件
- 🔐 **SSH隧道**: 通过SSH隧道连接远程数据库
- 👤 **用户关联**: 自动关联用户ID和邮箱信息
- 📱 **权限控制**: 只允许指定用户使用

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
```bash
python setup_wizard.py setup
```

### 3. 配置Telegram机器人
```bash
python setup_bot.py setup
```

### 4. 启动机器人
```bash
python start_bot.py
```

## 🤖 Telegram机器人使用

### 机器人命令
- `/start` - 开始使用机器人
- `/query 2025-05-20` - 查询指定日期数据
- `/query 2025-05-20 2025-05-25` - 查询日期范围数据
- `/batch` - 从5月20日到昨天批量导出
- `/batch 2025-05-20 2025-05-30` - 指定范围批量导出
- `/test` - 测试数据库连接
- `/help` - 查看帮助信息

### 使用示例
1. 在Telegram中搜索您的机器人
2. 发送 `/start` 开始使用
3. 发送 `/query 2025-05-20` 查询数据
4. 机器人会自动生成CSV文件并发送到聊天窗口

## 💻 命令行使用（可选）

如需直接使用命令行：
```bash
# 查询数据
python main.py query -s 2025-05-20

# 批量导出
python batch_export.py -s 2025-05-20 -e 2025-05-30
```

## ⚙️ 配置说明

### 数据库配置
- **MongoDB**: 存储CDN统计数据
- **MySQL**: 存储用户邮箱信息
- **SSH隧道**: 安全连接远程数据库

### 配置文件
- `.env` - 主配置文件
- `.env.bot` - 机器人配置文件

## 🔧 故障排除

### 测试连接
```bash
python main.py test-connection  # 测试MongoDB
python main.py test-mysql      # 测试MySQL
python main.py test-ssh        # 测试SSH隧道
```

### 常见问题
1. **连接失败**: 检查网络和认证信息
2. **SSH问题**: 验证密钥权限
3. **权限问题**: 确认数据库用户权限

## 📄 许可证

MIT License
