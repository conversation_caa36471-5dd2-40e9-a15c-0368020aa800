# CDN统计数据查询工具

基于MongoDB聚合查询的CDN统计数据查询工具，支持按时间范围查询用户流量统计。

## 功能特性

- 🔍 支持指定日期范围查询
- 📊 按用户ID分组统计流量
- 🌍 自动处理时区转换（北京时间 ↔ UTC）
- 📋 多种输出格式（表格、JSON）
- 📁 支持导出到CSV文件
- ⚡ 命令行界面，使用简单

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 复制环境变量配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置你的MongoDB连接信息：
```env
MONGODB_URI=mongodb://your-host:27017/
DATABASE_NAME=your_database_name
COLLECTION_NAME=cdn_statistics_total_metric
DEFAULT_TIMEZONE=Asia/Shanghai
```

## 使用方法

### 1. 测试数据库连接

```bash
python main.py test-connection
```

### 2. 查询指定日期的数据

```bash
# 查询单天数据（2025-05-20）
python main.py query -s "2025-05-20"

# 查询指定时间范围
python main.py query -s "2025-05-20" -e "2025-05-21"

# 查询并限制返回前10条记录
python main.py query -s "2025-05-20" -l 10

# 导出结果到CSV文件
python main.py query -s "2025-05-20" -o "result.csv"

# 以JSON格式输出
python main.py query -s "2025-05-20" -f json
```

### 3. 快速查询

```bash
# 快速查询指定日期（显示前10条）
python main.py quick-query -d "2025-05-20"

# 快速查询昨天的数据
python main.py quick-query
```

## 命令参数说明

### query 命令

- `-s, --start-date`: 开始日期（必需），格式：`2025-05-20` 或 `2025-05-20 00:00:00`
- `-e, --end-date`: 结束日期（可选），格式同上
- `-m, --metric-type`: 指标类型，默认为 `access`
- `-l, --limit`: 限制返回结果数量
- `-o, --export`: 导出结果到CSV文件
- `-f, --format`: 输出格式，可选 `table` 或 `json`

## 示例输出

```
查询时间范围 (UTC): 2025-05-19 16:00:00 到 2025-05-20 16:00:00
查询完成，共找到 5 条记录

查询结果:
+------+----------+----------------+----------------+
| 排名 | 用户ID   | 总流量(字节)   | 总流量(可读)   |
+======+==========+================+================+
| 1    | user123  | 1073741824     | 1.00 GB        |
+------+----------+----------------+----------------+
| 2    | user456  | 536870912      | 512.00 MB      |
+------+----------+----------------+----------------+
| 3    | user789  | 268435456      | 256.00 MB      |
+------+----------+----------------+----------------+

统计信息:
用户总数: 3
总流量: 1.75 GB
```

## 原始MongoDB查询

工具基于以下MongoDB聚合查询：

```javascript
db.getCollection("cdn_statistics_total_metric").aggregate([
  {
    $match: {
      metric_type: "access",
      statistics_time: {
        $gte: new Date("2025-05-19T16:00:00Z"),
        $lt: new Date("2025-05-20T16:00:00Z")
      }
    }
  },
  {
    $group: {
      _id: "$user_id",
      total_flux: { $sum: "$flux" }
    }
  },
  {
    $sort: { total_flux: -1 }
  }
]);
```

## 注意事项

- 时间输入使用北京时间，工具会自动转换为UTC时间进行查询
- 确保MongoDB服务正在运行且可访问
- 大量数据查询时建议使用 `--limit` 参数限制结果数量
