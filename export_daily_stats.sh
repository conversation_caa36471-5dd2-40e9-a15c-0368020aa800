#!/bin/bash

# 简化版CDN统计数据批量导出脚本
# 从2025-05-20开始到昨天，按日期生成CSV文件

echo "🚀 开始批量导出CDN统计数据..."
echo "================================"

# 设置开始日期
START_DATE="2025-05-20"

# 获取昨天的日期
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    END_DATE=$(date -v-1d +%Y-%m-%d)
else
    # Linux
    END_DATE=$(date -d "yesterday" +%Y-%m-%d)
fi

echo "📅 导出日期范围: $START_DATE 到 $END_DATE"

# 创建输出目录
OUTPUT_DIR="csv_exports_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "📁 输出目录: $OUTPUT_DIR"

# 初始化计数器
SUCCESS_COUNT=0
FAILED_COUNT=0
CURRENT_DATE="$START_DATE"

# 循环导出每一天的数据
while [[ "$CURRENT_DATE" != $(if [[ "$OSTYPE" == "darwin"* ]]; then date -j -v+1d -f "%Y-%m-%d" "$END_DATE" +%Y-%m-%d; else date -d "$END_DATE + 1 day" +%Y-%m-%d; fi) ]]; do
    
    echo ""
    echo "📊 正在导出 $CURRENT_DATE 的数据..."
    
    # 设置输出文件名
    CSV_FILE="$OUTPUT_DIR/cdn_stats_$CURRENT_DATE.csv"
    
    # 执行查询并导出CSV
    python main.py query -s "$CURRENT_DATE" -o "$CSV_FILE"
    
    # 检查是否成功
    if [ $? -eq 0 ] && [ -f "$CSV_FILE" ]; then
        # 计算记录数（减去标题行）
        RECORD_COUNT=$(($(wc -l < "$CSV_FILE") - 1))
        if [ "$RECORD_COUNT" -gt 0 ]; then
            echo "✅ $CURRENT_DATE: 导出成功，共 $RECORD_COUNT 条记录"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        else
            echo "⚠️  $CURRENT_DATE: 文件已生成但无数据记录"
            FAILED_COUNT=$((FAILED_COUNT + 1))
        fi
    else
        echo "❌ $CURRENT_DATE: 导出失败"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
    
    # 获取下一天日期
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        CURRENT_DATE=$(date -j -v+1d -f "%Y-%m-%d" "$CURRENT_DATE" +%Y-%m-%d)
    else
        # Linux
        CURRENT_DATE=$(date -d "$CURRENT_DATE + 1 day" +%Y-%m-%d)
    fi
    
    # 短暂延迟，避免对数据库造成压力
    sleep 1
done

echo ""
echo "================================"
echo "🎉 批量导出完成！"
echo ""
echo "📈 统计结果:"
echo "   成功导出: $SUCCESS_COUNT 天"
echo "   失败/无数据: $FAILED_COUNT 天"
echo ""
echo "📁 输出目录: $OUTPUT_DIR"
echo ""

# 生成文件列表
echo "📋 生成的文件列表:"
ls -la "$OUTPUT_DIR"/*.csv 2>/dev/null | while read line; do
    echo "   $line"
done

# 生成简单的汇总文件
SUMMARY_FILE="$OUTPUT_DIR/summary.txt"
{
    echo "CDN统计数据导出汇总"
    echo "==================="
    echo "导出时间: $(date)"
    echo "日期范围: $START_DATE 到 $END_DATE"
    echo "成功导出: $SUCCESS_COUNT 天"
    echo "失败/无数据: $FAILED_COUNT 天"
    echo ""
    echo "文件列表:"
    ls -1 "$OUTPUT_DIR"/*.csv 2>/dev/null | while read file; do
        filename=$(basename "$file")
        record_count=$(($(wc -l < "$file") - 1))
        echo "$filename: $record_count 条记录"
    done
} > "$SUMMARY_FILE"

echo ""
echo "📄 汇总报告已生成: $SUMMARY_FILE"
echo ""
echo "💡 使用提示:"
echo "   查看汇总: cat $SUMMARY_FILE"
echo "   合并所有CSV: cat $OUTPUT_DIR/*.csv > all_data.csv"
echo "   查看某天数据: cat $OUTPUT_DIR/cdn_stats_2025-05-20.csv"
