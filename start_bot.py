#!/usr/bin/env python3
"""
CDN统计查询工具 - Telegram机器人启动脚本
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from telegram_bot import CDNTelegramBot
from bot_config import BotConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('bot.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_env_file(env_file: str):
    """加载环境变量文件"""
    if not os.path.exists(env_file):
        return False
    
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
        return True
    except ImportError:
        print("⚠️  python-dotenv 未安装，请手动设置环境变量")
        return False


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'python-telegram-bot',
        'pymongo',
        'pymysql',
        'paramiko',
        'sshtunnel',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'python-telegram-bot':
                import telegram
            elif package == 'pymongo':
                import pymongo
            elif package == 'pymysql':
                import pymysql
            elif package == 'paramiko':
                import paramiko
            elif package == 'sshtunnel':
                import sshtunnel
            elif package == 'python-dotenv':
                import dotenv
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_configuration():
    """检查配置"""
    print("🔍 检查配置...")
    
    # 检查主配置文件
    if not os.path.exists('.env'):
        print("❌ 主配置文件 .env 不存在")
        print("请先运行配置向导: python setup_wizard.py setup")
        return False
    
    # 检查机器人配置
    bot_env_files = ['.env.bot', '.env']
    env_loaded = False
    
    for env_file in bot_env_files:
        if load_env_file(env_file):
            print(f"✅ 加载配置文件: {env_file}")
            env_loaded = True
            break
    
    if not env_loaded:
        print("⚠️  未找到机器人配置文件")
    
    # 检查必要的环境变量
    required_vars = ['TELEGRAM_BOT_TOKEN', 'TELEGRAM_ALLOWED_USERS']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ 缺少以下环境变量:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n请设置这些环境变量或创建 .env.bot 配置文件")
        return False
    
    print("✅ 配置检查通过")
    return True


def show_bot_info(config: BotConfig):
    """显示机器人信息"""
    print("\n🤖 CDN统计查询Telegram机器人")
    print("=" * 40)
    print(f"📋 允许的用户数量: {len(config.allowed_users)}")
    print(f"👑 管理员数量: {len(config.admin_users)}")
    print(f"📊 最大查询天数: {config.max_query_days}")
    print(f"📁 最大批量导出天数: {config.max_batch_days}")
    print(f"📄 单次查询最大记录数: {config.max_records_per_query}")
    print(f"💾 最大文件大小: {config.max_file_size_mb}MB")
    print(f"🚦 限流: {config.rate_limit_per_user} 请求/分钟/用户")
    print("=" * 40)


def create_temp_directory(temp_dir: str):
    """创建临时目录"""
    try:
        os.makedirs(temp_dir, exist_ok=True)
        print(f"📁 临时目录: {temp_dir}")
    except Exception as e:
        print(f"⚠️  无法创建临时目录 {temp_dir}: {e}")


async def main():
    """主函数"""
    print("🚀 启动CDN统计查询Telegram机器人")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查配置
    if not check_configuration():
        sys.exit(1)
    
    try:
        # 加载配置
        config = BotConfig.from_env()
        
        # 显示机器人信息
        show_bot_info(config)
        
        # 创建临时目录
        create_temp_directory(config.temp_dir)
        
        # 创建并启动机器人
        bot = CDNTelegramBot(config.bot_token, config.allowed_users)
        
        print("\n🎯 机器人启动中...")
        print("按 Ctrl+C 停止机器人")
        print("=" * 50)
        
        await bot.run()
        
    except KeyboardInterrupt:
        print("\n🛑 机器人已停止")
    except Exception as e:
        print(f"❌ 机器人启动失败: {e}")
        logging.error(f"Bot startup failed: {e}")
        sys.exit(1)


def show_help():
    """显示帮助信息"""
    help_text = """
CDN统计查询工具 - Telegram机器人

使用方法:
  python start_bot.py              启动机器人
  python start_bot.py --help       显示此帮助
  python bot_config.py             创建配置模板

配置步骤:
1. 创建Telegram机器人:
   - 发送 /newbot 给 @BotFather
   - 设置机器人名称和用户名
   - 获取Bot Token

2. 获取用户ID:
   - 发送消息给 @userinfobot
   - 获取您的用户ID

3. 配置环境变量:
   - 创建 .env.bot 文件
   - 设置 TELEGRAM_BOT_TOKEN
   - 设置 TELEGRAM_ALLOWED_USERS

4. 启动机器人:
   python start_bot.py

机器人命令:
  /start    - 开始使用
  /help     - 查看帮助
  /query    - 查询数据
  /batch    - 批量导出
  /test     - 测试连接
  /status   - 查看状态

示例:
  /query 2025-05-20
  /query 2025-05-20 2025-05-25
  /batch
  /batch 2025-05-20 2025-05-30
"""
    print(help_text)


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        show_help()
    else:
        asyncio.run(main())
