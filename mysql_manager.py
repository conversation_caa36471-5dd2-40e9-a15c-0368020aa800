import pymysql
from typing import Dict, List, Optional
from config import Config


class MySQLManager:
    """MySQL数据库管理器，用于查询用户信息，支持SSH隧道"""

    def __init__(self, tunnel_manager=None):
        """初始化MySQL连接"""
        self.config = Config.get_mysql_config()
        self.tunnel_manager = tunnel_manager
        self.connection = None
        self.user_cache = {}  # 用户信息缓存

        if self.config['enable_lookup']:
            self._connect()

    def _connect(self):
        """连接到MySQL数据库"""
        try:
            # 确定连接参数（是否使用SSH隧道）
            if self.config['use_ssh_tunnel'] and self.tunnel_manager:
                # 使用SSH隧道连接
                tunnel_config = self.tunnel_manager.get_local_mysql_config()
                host = tunnel_config['host']
                port = tunnel_config['port']
                print(f"通过SSH隧道连接MySQL: {host}:{port}")
            else:
                # 直接连接
                host = self.config['host']
                port = self.config['port']
                print(f"直接连接MySQL: {host}:{port}")

            self.connection = pymysql.connect(
                host=host,
                port=port,
                user=self.config['username'],
                password=self.config['password'],
                database=self.config['database'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            print(f"成功连接到MySQL数据库: {self.config['database']}")
        except Exception as e:
            print(f"连接MySQL失败: {e}")
            raise

    def close_connection(self):
        """关闭MySQL连接"""
        if self.connection:
            self.connection.close()
            print("MySQL连接已关闭")

    def get_user_email(self, user_id: str) -> Optional[str]:
        """
        根据用户ID获取用户email

        Args:
            user_id: 用户ID

        Returns:
            用户email，如果未找到返回None
        """
        if not self.config['enable_lookup']:
            return None

        # 检查缓存
        if user_id in self.user_cache:
            return self.user_cache[user_id]

        try:
            with self.connection.cursor() as cursor:
                sql = f"""
                SELECT {self.config['user_email_field']}
                FROM {self.config['user_table']}
                WHERE {self.config['user_id_field']} = %s
                """
                cursor.execute(sql, (user_id,))
                result = cursor.fetchone()

                if result:
                    email = result[self.config['user_email_field']]
                    # 缓存结果
                    self.user_cache[user_id] = email
                    return email
                else:
                    # 缓存空结果，避免重复查询
                    self.user_cache[user_id] = None
                    return None

        except Exception as e:
            print(f"查询用户email失败 (用户ID: {user_id}): {e}")
            return None

    def batch_get_user_emails(self, user_ids: List[str]) -> Dict[str, str]:
        """
        批量获取用户email

        Args:
            user_ids: 用户ID列表

        Returns:
            用户ID到email的映射字典
        """
        if not self.config['enable_lookup']:
            return {}

        result_map = {}
        uncached_ids = []

        # 先从缓存中获取
        for user_id in user_ids:
            if user_id in self.user_cache:
                cached_email = self.user_cache[user_id]
                if cached_email is not None:
                    result_map[user_id] = cached_email
            else:
                uncached_ids.append(user_id)

        # 批量查询未缓存的用户
        if uncached_ids:
            try:
                with self.connection.cursor() as cursor:
                    # 构建IN查询
                    placeholders = ','.join(['%s'] * len(uncached_ids))
                    sql = f"""
                    SELECT {self.config['user_id_field']}, {self.config['user_email_field']}
                    FROM {self.config['user_table']}
                    WHERE {self.config['user_id_field']} IN ({placeholders})
                    """
                    cursor.execute(sql, uncached_ids)
                    results = cursor.fetchall()

                    # 处理查询结果
                    found_ids = set()
                    for row in results:
                        user_id = str(row[self.config['user_id_field']])
                        email = row[self.config['user_email_field']]
                        result_map[user_id] = email
                        self.user_cache[user_id] = email
                        found_ids.add(user_id)

                    # 缓存未找到的用户ID
                    for user_id in uncached_ids:
                        if user_id not in found_ids:
                            self.user_cache[user_id] = None

            except Exception as e:
                print(f"批量查询用户email失败: {e}")

        return result_map

    def test_connection(self) -> bool:
        """测试MySQL连接"""
        if not self.config['enable_lookup']:
            print("用户email查询功能未启用")
            return True

        try:
            with self.connection.cursor() as cursor:
                # 测试查询
                cursor.execute("SELECT 1")
                result = cursor.fetchone()

                # 检查用户表是否存在
                cursor.execute(f"SHOW TABLES LIKE '{self.config['user_table']}'")
                table_exists = cursor.fetchone()

                if not table_exists:
                    print(f"警告: 用户表 '{self.config['user_table']}' 不存在")
                    return False

                # 检查字段是否存在
                cursor.execute(f"DESCRIBE {self.config['user_table']}")
                columns = cursor.fetchall()
                column_names = [col['Field'] for col in columns]

                required_fields = [
                    self.config['user_id_field'],
                    self.config['user_email_field']
                ]

                missing_fields = [field for field in required_fields if field not in column_names]
                if missing_fields:
                    print(f"警告: 缺少字段 {missing_fields}")
                    return False

                print("MySQL连接测试成功")
                return True

        except Exception as e:
            print(f"MySQL连接测试失败: {e}")
            return False

    def get_user_count(self) -> int:
        """获取用户总数"""
        if not self.config['enable_lookup']:
            return 0

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) as count FROM {self.config['user_table']}")
                result = cursor.fetchone()
                return result['count'] if result else 0
        except Exception as e:
            print(f"获取用户总数失败: {e}")
            return 0

    def clear_cache(self):
        """清空用户缓存"""
        self.user_cache.clear()
        print("用户缓存已清空")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_connection()
