#!/bin/bash

# CDN统计查询工具 - Telegram机器人部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        print_error "未找到Python，请先安装Python 3.8或更高版本"
        exit 1
    fi
    
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    print_success "Python版本: $PYTHON_VERSION"
}

# 检查pip
check_pip() {
    print_info "检查pip..."
    
    if command -v pip3 &> /dev/null; then
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        PIP_CMD="pip"
    else
        print_error "未找到pip"
        exit 1
    fi
    
    print_success "pip可用"
}

# 安装依赖
install_dependencies() {
    print_info "安装机器人依赖..."
    
    if [ -f "requirements_bot.txt" ]; then
        $PIP_CMD install -r requirements_bot.txt
        print_success "依赖安装完成"
    else
        print_warning "requirements_bot.txt 不存在，手动安装依赖..."
        $PIP_CMD install python-telegram-bot pymongo pymysql paramiko sshtunnel python-dotenv
    fi
}

# 检查主配置
check_main_config() {
    print_info "检查主配置..."
    
    if [ ! -f ".env" ]; then
        print_error "主配置文件 .env 不存在"
        print_info "请先运行: python setup_wizard.py setup"
        exit 1
    fi
    
    print_success "主配置文件存在"
}

# 配置机器人
setup_bot_config() {
    print_info "配置Telegram机器人..."
    
    if [ -f ".env.bot" ]; then
        print_warning ".env.bot 已存在"
        if ! confirm "是否重新配置？"; then
            return
        fi
    fi
    
    print_info "运行机器人配置向导..."
    $PYTHON_CMD setup_bot.py setup
}

# 测试配置
test_config() {
    print_info "测试机器人配置..."
    
    $PYTHON_CMD setup_bot.py test
    
    if [ $? -eq 0 ]; then
        print_success "配置测试通过"
    else
        print_error "配置测试失败"
        exit 1
    fi
}

# 创建启动脚本
create_start_script() {
    print_info "创建启动脚本..."
    
    cat > start_cdn_bot.sh << 'EOF'
#!/bin/bash

# CDN Telegram机器人启动脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🚀 启动CDN Telegram机器人..."

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "❌ 主配置文件 .env 不存在"
    exit 1
fi

if [ ! -f ".env.bot" ]; then
    echo "❌ 机器人配置文件 .env.bot 不存在"
    exit 1
fi

# 启动机器人
python3 start_bot.py

EOF

    chmod +x start_cdn_bot.sh
    print_success "启动脚本已创建: start_cdn_bot.sh"
}

# 创建系统服务
create_systemd_service() {
    print_info "创建系统服务..."
    
    local current_dir=$(pwd)
    local current_user=$(whoami)
    
    cat > cdn-telegram-bot.service << EOF
[Unit]
Description=CDN Statistics Telegram Bot
After=network.target

[Service]
Type=simple
User=$current_user
WorkingDirectory=$current_dir
ExecStart=/usr/bin/python3 start_bot.py
Restart=always
RestartSec=10
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
EOF

    print_success "系统服务文件已创建: cdn-telegram-bot.service"
    print_info "安装服务命令:"
    print_info "  sudo cp cdn-telegram-bot.service /etc/systemd/system/"
    print_info "  sudo systemctl enable cdn-telegram-bot"
    print_info "  sudo systemctl start cdn-telegram-bot"
}

# 创建Docker配置
create_docker_config() {
    print_info "创建Docker配置..."
    
    # Dockerfile
    cat > Dockerfile.bot << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    openssh-client \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements_bot.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements_bot.txt

# 复制应用代码
COPY *.py ./
COPY *.md ./
COPY .env ./
COPY .env.bot ./

# 创建临时目录
RUN mkdir -p /tmp/cdn_bot

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 创建非root用户
RUN useradd -m -u 1000 botuser && chown -R botuser:botuser /app
USER botuser

# 启动命令
CMD ["python", "start_bot.py"]
EOF

    # docker-compose.yml
    cat > docker-compose.bot.yml << 'EOF'
version: '3.8'

services:
  cdn-telegram-bot:
    build:
      context: .
      dockerfile: Dockerfile.bot
    container_name: cdn-telegram-bot
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - /tmp/cdn_bot:/tmp/cdn_bot
    environment:
      - PYTHONUNBUFFERED=1
    networks:
      - cdn-network

networks:
  cdn-network:
    driver: bridge
EOF

    print_success "Docker配置已创建:"
    print_info "  Dockerfile.bot"
    print_info "  docker-compose.bot.yml"
}

# 确认函数
confirm() {
    local prompt="$1"
    local response
    
    while true; do
        read -p "$prompt (y/n): " response
        case $response in
            [Yy]* ) return 0;;
            [Nn]* ) return 1;;
            * ) echo "请输入 y 或 n";;
        esac
    done
}

# 显示部署信息
show_deployment_info() {
    print_success "🎉 Telegram机器人部署完成！"
    echo ""
    print_info "📁 生成的文件:"
    echo "  - start_cdn_bot.sh (启动脚本)"
    echo "  - cdn-telegram-bot.service (系统服务)"
    echo "  - Dockerfile.bot (Docker镜像)"
    echo "  - docker-compose.bot.yml (Docker编排)"
    echo ""
    print_info "🚀 启动方式:"
    echo "  1. 直接启动: ./start_cdn_bot.sh"
    echo "  2. 后台启动: nohup ./start_cdn_bot.sh > bot.log 2>&1 &"
    echo "  3. 系统服务: sudo systemctl start cdn-telegram-bot"
    echo "  4. Docker启动: docker-compose -f docker-compose.bot.yml up -d"
    echo ""
    print_info "📱 使用机器人:"
    echo "  1. 在Telegram中搜索您的机器人"
    echo "  2. 发送 /start 开始使用"
    echo "  3. 发送 /help 查看命令帮助"
    echo ""
    print_info "📖 详细文档: TELEGRAM_BOT_GUIDE.md"
}

# 主菜单
show_menu() {
    echo ""
    echo "CDN统计查询工具 - Telegram机器人部署"
    echo "======================================"
    echo "1. 完整部署 (推荐)"
    echo "2. 只安装依赖"
    echo "3. 只配置机器人"
    echo "4. 只测试配置"
    echo "5. 创建启动脚本"
    echo "6. 创建系统服务"
    echo "7. 创建Docker配置"
    echo "8. 退出"
    echo ""
}

# 主函数
main() {
    print_info "CDN统计查询工具 - Telegram机器人部署脚本"
    echo "=" * 50
    
    # 检查环境
    check_python
    check_pip
    
    # 如果有参数，直接执行
    if [ $# -gt 0 ]; then
        case $1 in
            "install")
                install_dependencies
                ;;
            "config")
                setup_bot_config
                ;;
            "test")
                test_config
                ;;
            "full")
                check_main_config
                install_dependencies
                setup_bot_config
                test_config
                create_start_script
                create_systemd_service
                create_docker_config
                show_deployment_info
                ;;
            *)
                echo "用法: $0 [install|config|test|full]"
                exit 1
                ;;
        esac
        return
    fi
    
    # 交互式菜单
    while true; do
        show_menu
        read -p "请选择操作 (1-8): " choice
        
        case $choice in
            1)
                check_main_config
                install_dependencies
                setup_bot_config
                test_config
                create_start_script
                create_systemd_service
                create_docker_config
                show_deployment_info
                break
                ;;
            2)
                install_dependencies
                ;;
            3)
                setup_bot_config
                ;;
            4)
                test_config
                ;;
            5)
                create_start_script
                ;;
            6)
                create_systemd_service
                ;;
            7)
                create_docker_config
                ;;
            8)
                print_info "退出部署脚本"
                break
                ;;
            *)
                print_error "无效选择，请输入 1-8"
                ;;
        esac
    done
}

# 运行主函数
main "$@"
