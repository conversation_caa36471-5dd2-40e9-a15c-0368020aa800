#!/usr/bin/env python3
"""
SSH隧道配置助手
专门用于配置通过SSH隧道连接MongoDB的场景
"""

import click
import os
from urllib.parse import quote_plus


def create_ssh_tunnel_config():
    """创建SSH隧道配置"""
    click.echo("=== SSH隧道 + MongoDB配置助手 ===\n")
    
    click.echo("您的MongoDB信息:")
    click.echo("- 主机: *************:27017")
    click.echo("- 用户: mongouser")
    click.echo("- 认证数据库: admin")
    click.echo("- 连接方式: 通过SSH隧道\n")
    
    # SSH服务器配置
    click.echo("--- SSH服务器配置 ---")
    ssh_host = click.prompt('SSH服务器地址')
    ssh_port = click.prompt('SSH端口', default=22, type=int)
    ssh_username = click.prompt('SSH用户名')
    
    # SSH认证方式
    auth_method = click.prompt(
        'SSH认证方式',
        type=click.Choice(['key', 'password']),
        default='key'
    )
    
    ssh_password = ""
    ssh_key_file = ""
    
    if auth_method == 'key':
        ssh_key_file = click.prompt('SSH私钥文件路径', default='~/.ssh/id_rsa')
        # 展开用户目录
        ssh_key_file = os.path.expanduser(ssh_key_file)
    else:
        ssh_password = click.prompt('SSH密码', hide_input=True)
    
    # MongoDB认证信息
    click.echo("\n--- MongoDB认证信息 ---")
    mongodb_password = click.prompt('MongoDB密码（mongouser的密码）', hide_input=True)
    
    # 数据库配置
    click.echo("\n--- 数据库配置 ---")
    database_name = click.prompt('数据库名称')
    collection_name = click.prompt('集合名称', default='cdn_statistics_total_metric')
    
    # 隧道端口
    local_tunnel_port = click.prompt('本地隧道端口', default=27018, type=int)
    
    # MySQL配置
    click.echo("\n--- MySQL配置（用于查询用户邮箱） ---")
    enable_mysql = click.confirm('是否启用用户邮箱查询功能？', default=True)
    
    mysql_config = {}
    if enable_mysql:
        mysql_config['host'] = click.prompt('MySQL主机地址（可能也需要通过SSH访问）')
        mysql_config['port'] = click.prompt('MySQL端口', default=3306, type=int)
        mysql_config['username'] = click.prompt('MySQL用户名')
        mysql_config['password'] = click.prompt('MySQL密码', hide_input=True)
        mysql_config['database'] = click.prompt('MySQL数据库名')
        mysql_config['table'] = click.prompt('用户表名', default='vc_user')
        mysql_config['id_field'] = click.prompt('用户ID字段名', default='id')
        mysql_config['email_field'] = click.prompt('用户邮箱字段名', default='email')
    
    # 生成配置文件
    config_content = f"""# MongoDB 配置（通过SSH隧道连接）
# 注意：在SSH隧道模式下，MONGODB_URI 会被自动构建
MONGODB_URI=mongodb://localhost:27017/
DATABASE_NAME={database_name}
COLLECTION_NAME={collection_name}

# SSH 隧道配置（启用）
USE_SSH_TUNNEL=true
SSH_HOST={ssh_host}
SSH_PORT={ssh_port}
SSH_USERNAME={ssh_username}"""

    if auth_method == 'password':
        config_content += f"""
SSH_PASSWORD={ssh_password}
SSH_KEY_FILE="""
    else:
        config_content += f"""
SSH_PASSWORD=
SSH_KEY_FILE={ssh_key_file}"""

    config_content += f"""

# 远程MongoDB配置（通过SSH隧道访问的实际MongoDB）
REMOTE_MONGODB_HOST=*************
REMOTE_MONGODB_PORT=27017
REMOTE_MONGODB_USERNAME=mongouser
REMOTE_MONGODB_PASSWORD={mongodb_password}
REMOTE_MONGODB_AUTH_SOURCE=admin

# 本地隧道端口
LOCAL_TUNNEL_PORT={local_tunnel_port}

# MySQL 配置（用于查询用户email）
ENABLE_USER_EMAIL_LOOKUP={str(enable_mysql).lower()}"""

    if enable_mysql:
        config_content += f"""
MYSQL_HOST={mysql_config['host']}
MYSQL_PORT={mysql_config['port']}
MYSQL_USERNAME={mysql_config['username']}
MYSQL_PASSWORD={mysql_config['password']}
MYSQL_DATABASE={mysql_config['database']}
MYSQL_USER_TABLE={mysql_config['table']}
MYSQL_USER_ID_FIELD={mysql_config['id_field']}
MYSQL_USER_EMAIL_FIELD={mysql_config['email_field']}"""
    else:
        config_content += """
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USERNAME=
MYSQL_PASSWORD=
MYSQL_DATABASE=
MYSQL_USER_TABLE=vc_user
MYSQL_USER_ID_FIELD=id
MYSQL_USER_EMAIL_FIELD=email"""

    config_content += """

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai
"""

    # 写入配置文件
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    click.echo(f"\n✅ SSH隧道配置已保存到 .env")
    
    # 显示连接信息
    click.echo(f"\n--- 连接信息 ---")
    click.echo(f"SSH隧道: {ssh_username}@{ssh_host}:{ssh_port}")
    click.echo(f"远程MongoDB: mongouser@*************:27017")
    click.echo(f"本地隧道端口: {local_tunnel_port}")
    
    # 显示下一步操作
    click.echo("\n--- 下一步操作 ---")
    click.echo("1. 测试SSH隧道: python main.py test-ssh")
    click.echo("2. 测试MongoDB连接: python main.py test-connection")
    if enable_mysql:
        click.echo("3. 测试MySQL连接: python main.py test-mysql")
    click.echo("4. 开始查询数据: python main.py query -s 2025-05-20")
    
    # 显示手动SSH隧道命令（供参考）
    click.echo(f"\n--- 手动SSH隧道命令（供参考） ---")
    if auth_method == 'key':
        click.echo(f"ssh -i {ssh_key_file} -L {local_tunnel_port}:*************:27017 {ssh_username}@{ssh_host}")
    else:
        click.echo(f"ssh -L {local_tunnel_port}:*************:27017 {ssh_username}@{ssh_host}")


@click.group()
def cli():
    """SSH隧道配置助手"""
    pass


@cli.command()
def setup():
    """配置SSH隧道连接"""
    create_ssh_tunnel_config()


@cli.command()
def show():
    """显示当前配置（隐藏密码）"""
    if not os.path.exists('.env'):
        click.echo("❌ .env文件不存在")
        return
    
    click.echo("=== 当前SSH隧道配置 ===")
    with open('.env', 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
        for line in lines:
            if 'PASSWORD' in line and '=' in line:
                key, value = line.split('=', 1)
                if value:
                    click.echo(f"{key}=***")
                else:
                    click.echo(line)
            else:
                click.echo(line)


if __name__ == '__main__':
    cli()
